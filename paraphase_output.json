{"StartNode": {"original": "The starting point for all workflows. Only nodes connected to this node will be executed.", "paraphrases": ["This is the initial node for any workflow; only nodes linked to it will be processed.", "All workflows commence with this node, and execution is restricted to its directly connected components.", "Serving as the origin for all operational sequences, it only runs nodes that are attached to it.", "The essential starting point for every process, it ensures that only connected nodes are executed.", "From this node, all workflows initiate, and only those nodes physically connected to it will be active."], "asking": ["We need a foundational node that serves as the absolute beginning for every workflow. It's critical that only the nodes directly connected to this starting point are ever executed.", "I'm looking for the primary node that kicks off all processes. The rule should be that only subsequent nodes explicitly linked to this initial node will actually be run.", "Could you implement an entry point node for our workflows? This node must be the one that initiates everything, and only the components directly downstream from it get activated.", "We require a designated node to mark the very start of any workflow. Its function is to ensure that execution will solely proceed to nodes that are attached to it.", "Let's create a universal 'start' node. This node should be mandatory for all workflows, acting as the trigger for execution, and only its connected successors will ever become active."], "keywords": ["StartNode", "Start", "Starting point", "Workflows", "Execution", "Entry point", "Root"], "id": 0}, "MCP_Script_Generation_script_generate": {"original": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "paraphrases": ["Input a topic and keyword into the script generator.", "Supply the script generation tool with a topic and a relevant keyword.", "Enter the topic and keyword for the script creation program.", "Give the script generator a topic and a keyword.", "Specify a topic and keyword for the script-writing software."], "asking": ["I need a node that can go to a bunch of web pages and pull out the main text content, maybe even from a specific section if I tell it where to look.", "Could you create a tool that takes a list of website addresses, visits them, and then gives me back all the text it finds, optionally filtered by a CSS query?", "I'm looking for a way to automatically grab the text from multiple URLs. It should return a structured output with the URL and the text, and ideally allow for targeting specific elements on the page.", "Build me a component that can systematically fetch content from a given set of URLs. The core functionality is to extract the body text or text within a provided CSS selector for each URL.", "I require a node for web content extraction. Its purpose is to process a list of URLs, scrape the textual data (either full page or selector-based), and output it as a list of URL-text pairs."], "keywords": ["MCP", "Script Generation", "Generator", "Topic", "Keyword", "<PERSON><PERSON><PERSON>"], "id": 1}, "MCP_Voice_generation_generate_audio": {"original": "Generate video audio using the script", "paraphrases": ["Create audio for the video from the script.", "Produce the video's soundtrack according to the provided script.", "Synthesize the audio component of the video utilizing the given script.", "Develop sound for the video based on the text of the script.", "Make the video's audio track by following the script."], "asking": ["I need to create the audio narration for my video, and I have the script ready. Can you handle that?", "Could you help me turn this written script into the actual audio track that will go with my video?", "I'm looking for a way to generate the spoken audio content for my video directly from a text script.", "How can I generate the audio component of my video where the spoken words are taken directly from a provided script?", "I require a functionality that can synthesize an audio track suitable for video, utilizing a given textual script as its source."], "keywords": ["MCP", "Voice generation", "Video audio"], "id": 2}, "MCP_Voice_generation_fetch_audio": {"original": "Fetch audio generated files links using ids", "paraphrases": ["Retrieve the links to the generated audio files by their IDs.", "Obtain the URLs for audio files that have been created, using their unique identifiers.", "Access the web addresses of the produced audio content based on the provided IDs.", "Get the links for the generated audio outputs, specifying them by their IDs.", "Locate and return the file links for the audio generated, cross-referencing with the given IDs."], "asking": ["I need a way to retrieve the direct links to audio files, given their unique generation identifiers.", "Could you create a node that allows me to input audio file IDs and get back the corresponding URLs for the generated audio content?", "I'm looking for a component that takes a specific audio generation ID and outputs the public or access link to that generated audio file.", "How can I programmatically obtain the URLs for previously generated audio files if I already have their assigned IDs?", "I require a mechanism to resolve generated audio content identifiers into their respective access or download links."], "keywords": ["MCP", "Voice Generation", "Audio", "<PERSON>tch", "Links", "IDs"], "id": 3}, "MCP_Candidate_Interview_candidate_suitability_pre": {"original": "Analyze candidate suitability based on job description and resume links", "paraphrases": ["Evaluate applicant qualifications using the job specification and provided CV links.", "Determine candidate fit by reviewing their resumes (via links) against the job requirements.", "Assess the appropriateness of applicants by comparing the job brief with their linked curriculum vitae.", "Examine how well candidates meet the role's criteria, referencing both the job listing and their online CVs.", "Gauge prospective employees' suitability by cross-referencing the job's demands with their submitted resume links."], "asking": ["Could you implement a process that evaluates how well candidate resumes align with a specific job description to assess their fit for the role?", "I need a function that takes a job posting and a collection of applicant resumes, then provides an assessment of each candidate's suitability.", "Set up a node that will compare incoming resumes against a given job description and score or rank candidates based on their qualifications and experience.", "Can we automate the step where we determine if an applicant is a good match for a job, using the job's requirements and their provided resume details?", "Please build a module that ingests both a job description and a set of resume links, then generates a suitability report for each candidate."], "keywords": ["MCP", "Candidate", "Suitability", "Analysis", "Pre-screening", "Recruitment", "Job Description", "Resume"], "id": 4}, "ApiRequestNode": {"original": "Makes a single HTTP request to the specified URL.", "paraphrases": ["Sends one HTTP request to the given address.", "Issues a singular HTTP call to the designated web address.", "Dispatches a sole HTTP query to the indicated URL.", "Initiates a unique HTTP communication with the provided URL.", "Performs one HTTP access operation to the particular URL."], "asking": ["Could we get a node that simply fetches content from a given web address?", "I'm looking for a way to make an outbound call to any URL, maybe to send data or get a response back.", "We require a utility to execute a one-time web request to a user-defined endpoint.", "Is there a component that allows us to interact with external APIs by hitting a specific URL?", "My use case needs a simple method to connect to and retrieve information from an internet address."], "keywords": ["API", "Request", "HTTP", "URL", "Endpoint", "Remote"], "id": 5}, "AgenticAI": {"original": "Executes an AI agent with tools and memory using AutoGen.", "paraphrases": ["AutoGen facilitates the execution of an AI agent equipped with tools and memory.", "An AI agent, complete with tools and memory, is run using the AutoGen framework.", "Through AutoGen, an AI agent possessing tools and memory capabilities is brought into operation.", "This system operates an AI agent, which includes tools and memory functions, via AutoGen.", "The AutoGen library is employed to execute an AI agent that utilizes tools and maintains memory."], "asking": ["I need a node that allows me to run an AI agent, specifically one that can utilize tools and maintain memory of its interactions, ideally built on AutoGen.", "Could you create a component that executes an AutoGen-based AI agent, making sure it supports agent capabilities like tool-use and persistent memory?", "We're looking for a way to deploy and run intelligent agents that can remember past states and leverage external tools. Can we get a node that encapsulates an AutoGen agent with these features?", "I require a node that orchestrates the execution of an AI agent, ensuring it has access to various tools and a reliable memory store, leveraging the AutoGen framework.", "Provide a node that functions as an executor for an AutoGen AI agent, complete with support for external tool integration and internal memory retention."], "keywords": ["AI agent", "AutoGen", "tools", "memory", "execution"], "id": 6}, "MCP_Candidate_Interview_generate_interview_agenda": {"original": "Generate interview agenda based on job description and resume", "paraphrases": ["Craft an interview schedule using the job description and the candidate's resume.", "Develop a structured interview plan from the job posting and the applicant's CV.", "Design an interview outline by referencing the job description and the resume.", "Formulate a tailored interview agenda considering both the job requirements and the candidate's qualifications.", "Prepare an interview framework based on the job specifications and the submitted resume."], "asking": ["Could you help me put together a structured plan for an upcoming interview? I'll provide the job description and the candidate's resume, and I'm looking for a solid agenda.", "I've got a job posting and a candidate's CV ready. What I really need is a well-thought-out sequence of topics and questions to cover during their interview, tailored to those documents.", "I'm prepping for an interview and want to make sure I hit all the key points. If I give you the job requirements and the applicant's background, can you draft an effective interview schedule for me?", "I need assistance in organizing the flow of an interview. Given a job description and a resume, could you outline a coherent agenda that helps me assess the candidate thoroughly?", "Is there a way to quickly generate a comprehensive interview agenda? I'd supply the job's details and the candidate's resume, and I'm hoping for an automated output that lays out the discussion points."], "keywords": ["interview agenda generation", "candidate interview", "job description analysis", "resume analysis", "recruitment process"], "id": 7}, "CombineTextComponent": {"original": "Joins text inputs with a separator, supporting a variable number of inputs.", "paraphrases": ["Combines multiple text strings using a specified delimiter, accommodating any number of inputs.", "Merges an arbitrary quantity of text segments together, separated by a particular character.", "Concatenates various text entries with a chosen separator, irrespective of their count.", "Connects an unspecified number of text values, inserting a distinct joining element between them.", "Unites several text inputs using a separator, designed to handle a fluctuating quantity."], "asking": ["I'm looking for a way to combine several text inputs into one unified string. The crucial part is that I need to be able to specify a separator that goes between each input, and the number of inputs won't always be the same.", "Is there a node that can take an arbitrary number of text fragments and stitch them together into a single, cohesive string? I'd also need the ability to define what character or string acts as a divider between each fragment.", "How can I concatenate multiple text fields – sometimes two, sometimes five, sometimes more – into a single output? I want to put something specific, like a comma and a space, between each joined piece.", "I need a text utility that can merge any number of input strings into one final output string. It's really important that I can customize the 'glue' or delimiter that's used for the merge operation.", "Could we get a feature that allows me to join a dynamic list of text inputs into one output text? I'd like the user to be able to set the joining string, such as a newline character or a hyphen, for separation."], "keywords": ["text", "string", "combine", "join", "concatenate", "separator", "multiple"], "id": 8}, "MCP_content-extractor-mcp_generate_subtitle": {"original": "Generate subtitles for a video by processing its audio.", "paraphrases": ["Create captions for a video through audio analysis.", "Transcribe a video's audio to produce its subtitles.", "Convert a video's spoken content into text captions using its sound.", "Extract the dialogue from a video's soundtrack to make subtitles.", "Formulate video subtitles by analyzing the accompanying audio."], "asking": ["I need a way to automatically generate subtitles for my videos by processing their audio tracks. Can we get a node that does that?", "Could you create a function that listens to a video's audio and then converts it into timed text captions?", "I'm looking for a component that can take the audio stream from a video and produce the corresponding dialogue as a subtitle file.", "We require a processing step that analyzes a video's sound content to automatically create subtitles.", "Develop a module that extracts spoken words from video audio and formats them into synchronized subtitle text."], "keywords": ["subtitle generation", "audio processing", "video", "speech-to-text", "content extraction"], "id": 9}, "MCP_Stock_Video_Generation_generate_stock_video": {"original": "generate and find the stock video for the video", "paraphrases": ["Source and create the necessary stock footage for the video.", "Locate and produce the appropriate stock video clips for the production.", "Obtain or generate the required stock visual content for the video project.", "Find and develop the stock video assets needed for the final video.", "Identify and create the stock video elements to be used in the video."], "asking": ["I need to source and provide appropriate stock video clips for my upcoming video.", "Could you please handle the retrieval and generation of stock video content to be used in the video project?", "My video requires visual elements; can this node create or search for relevant stock video assets?", "Please identify and deliver suitable stock footage segments to be incorporated into the video production.", "I'm looking for a way to automatically find and generate the right stock video segments for my project. Can this help with that?"], "keywords": ["stock video", "generate", "find", "MCP"], "id": 10}, "MCP_video-generation-mcp_generate_video": {"original": "generate and process the video", "paraphrases": ["Create and edit the footage.", "Produce and manipulate the visual content.", "Formulate and refine the moving images.", "Originate and manage the film.", "Construct and modify the audiovisual stream."], "asking": ["I need a capability that can both generate video content and perform various processing tasks on it.", "Could you implement a module responsible for creating video and then applying further modifications or analysis?", "My requirement is a component that handles video generation and its subsequent processing steps.", "I'm looking for a way to automatically create videos and then perform necessary operations like editing, encoding, or enhancement.", "Provide me with a function to synthesize video content and then process it according to specified parameters."], "keywords": ["video", "generation", "generate", "process"], "id": 11}, "MCP_Stock_Image_Generation_generate_ai_stock_image": {"original": "generate and find the stock image for the video", "paraphrases": ["Source and select a stock image to accompany the video.", "Obtain a suitable stock picture for the video production.", "Locate or create a stock visual for the video content.", "Find or produce a stock photograph for the video.", "Acquire a relevant stock image for the video clip."], "asking": ["I need a visual asset for my video. Can you either generate a new stock image or find an existing one that fits?", "For this video, I require a stock image. Please go ahead and create one or search for a suitable existing option.", "My video needs a stock photo. Could you handle generating a custom one or sourcing an appropriate image from a stock library?", "I'm working on a video and need a stock image. Please help me by either generating a new image or locating a relevant one.", "Generate or locate the best stock image to complement the video content."], "keywords": ["stock image", "AI image generation", "video content", "image search"], "id": 12}, "MCP_voice-generation-mcp_generate_audio": {"original": "Generate video audio using the script", "paraphrases": ["Create audio for the video based on the script.", "Produce the soundtrack for the video using the script.", "Synthesize the video's audio track from the given script.", "Develop the sound for the video according to the script.", "Transform the script into the video's audio component."], "asking": ["I need a function that can take a written script and convert it into audio suitable for a video project.", "Can you create a node that generates speech audio for a video using a provided text script as input?", "My goal is to transform a script into the audio track for a video. What's the process for doing that?", "I'm looking for a way to generate the voiceover for my video automatically from a script I've prepared.", "How can I synthesize audio from a text script to be used specifically as the soundtrack for a video?"], "keywords": ["MCP", "voice generation", "audio generation", "video audio"], "id": 13}, "MCP_voice-generation-mcp_fetch_audio": {"original": "Fetch audio generated files links using ids", "paraphrases": ["Retrieve the URLs of the generated audio files by using their IDs.", "Obtain the links to the audio files that were created, leveraging their identifiers.", "Access the web addresses of the produced audio files with the given IDs.", "Get the direct links for the generated audio output files, using their specific IDs.", "Using the provided IDs, acquire the links to the recently generated audio content."], "asking": ["I need a way to retrieve the URLs for our generated audio files, given their specific IDs.", "How can I get the direct links to the audio content that was created, using the unique identifiers assigned to each piece?", "Could you provide a function or API call that allows me to fetch the access links for audio files when I input their corresponding IDs?", "I'm looking for a method to resolve audio generation IDs into their respective download or playback links.", "Given an audio file's ID, I need to obtain the link where that generated audio can be accessed or downloaded."], "keywords": ["MCP", "voice-generation", "fetch", "audio", "links", "ids"], "id": 14}, "MCP_script-generation-mcp-server_generate_script": {"original": "Generate a script using OpenAI GPT-4o.", "paraphrases": ["Create a script using OpenAI's GPT-4o model.", "Develop a script with the help of OpenAI GPT-4o.", "Produce a script by leveraging OpenAI GPT-4o.", "Write a script, powered by OpenAI GPT-4o.", "Formulate a script using OpenAI GPT-4o technology."], "asking": ["I need a function to retrieve comprehensive user profile details. It should accept a user ID and return their name, email, registration date, and last login. Crucially, it must also gracefully handle scenarios where the provided user ID does not exist.", "Could you create a mechanism that allows me to fetch all relevant information for a specific user? When I provide a user's identifier, I expect to get their full profile, including their contact info and key activity timestamps. It's important that it reports back if the user can't be found.", "Develop a data access primitive named `GetUserData`. This primitive will consume a `userId` parameter and yield a `UserProfile` object. The `UserProfile` object is to contain `name`, `email`, `registrationDate`, and `lastLoginDate`. The system should explicitly indicate 'User Not Found' if the `userId` does not correspond to an existing record.", "I'm building a user management interface and require a reliable way to pull up a user's complete information. Given a user's ID, I want to see all their details – who they are, how to contact them, and their activity history. Please ensure it has a mechanism for when a user ID doesn't match any records.", "We need a simple utility to 'lookup user profile'. When provided with an ID, it should return the user's name, email, and their join/last-active dates. If the ID isn't valid, it should clearly state that the user wasn't found instead of an error."], "keywords": ["MCP", "script generation", "OpenAI", "GPT-4o", "AI"], "id": 15}, "DelayComponent": {"original": "Pauses the workflow execution for a set number of seconds.", "paraphrases": ["Temporarily stops the workflow's execution for a specified duration in seconds.", "Halts the ongoing process for a predetermined number of seconds.", "Suspends the workflow's progression for a configured time, measured in seconds.", "Introduces a delay in the execution of the task for a fixed number of seconds.", "Puts the operational workflow on hold for a precise duration, expressed in seconds."], "asking": ["Could we get a node that simply introduces a delay? I need the workflow to pause for a specific number of seconds before proceeding.", "I'm looking for a way to build in a configurable waiting period between steps. Something that holds the execution for a set amount of time.", "Is there a component that can temporarily suspend the workflow? I need it to effectively 'sleep' for X seconds before continuing.", "We need a node that allows us to insert a time-based hold. The process should halt for a user-defined duration before resuming.", "Can you add a feature where the workflow can be made to pause for a designated number of seconds, acting as a timed break in the execution?"], "keywords": ["delay", "pause", "workflow", "duration", "time"], "id": 16}, "SelectDataComponent": {"original": "Extracts elements from lists or dictionaries.", "paraphrases": ["Retrieves components from lists or dictionaries.", "Selects items contained within lists or dictionaries.", "Obtains data points from either lists or dictionaries.", "Pulls out individual members from lists or dictionaries.", "Isolates specific entries found in lists or dictionaries."], "asking": ["I need a node that can take a dictionary or a JSON object and retrieve the value associated with a specified key.", "Is there a node available that allows me to get a specific item from a list by providing its index?", "I often have structured data as input, and I need a way to select or filter down to just a particular element, whether it's by key in a map or position in a sequence.", "When I have a collection of items, like a list of records or a dictionary of settings, I need a node that can extract just one individual element or field from it.", "Could we get a node that helps me access and pull out individual components or values from either a list-like structure or a dictionary-like object?"], "keywords": ["select", "extract", "elements", "lists", "dictionaries"], "id": 17}, "LoopNode": {"original": "Iterates over a list, with advanced controls for parallelism, aggregation, and error handling.", "paraphrases": ["Processes items in a list, offering sophisticated management for concurrent execution, data collection, and fault tolerance.", "Traverses a list, providing enhanced features for parallel processing, result compilation, and exception management.", "Executes operations on a list, with powerful options for running tasks in parallel, consolidating outputs, and managing errors.", "Works through a list, equipped with sophisticated mechanisms for parallel task execution, data aggregation, and robust error handling.", "Cycles through a list's elements, featuring specialized controls for running tasks concurrently, gathering results, and addressing failures."], "asking": ["I need a node that can iterate through a list of items, but I also require advanced controls for running these operations in parallel to maximize performance. Additionally, it's essential that it can aggregate all the individual results and handle any errors gracefully without stopping the entire process.", "Can you create a robust looping mechanism? I'm looking for something that can process items from a collection, offer options for concurrent execution, combine all the outputs into a single result, and importantly, have built-in error handling for resilient execution.", "I'm looking for a powerful list processor. It needs to iterate over a dataset, allowing me to specify how many items process at once for speed, collect all the outcomes efficiently, and have fault tolerance so individual failures don't break the whole loop.", "We require a node for sequential task execution over an iterable, with capabilities for defining custom concurrency levels to optimize throughput. This node must also support the aggregation of results from all processed items and offer configurable error management strategies.", "I have a big list of jobs, and I need a 'smart' loop that can process them. It should be able to run multiple jobs at the same time to finish quicker, gather all the results for me, and make sure that if one job fails, the whole process doesn't stop, and I get proper error reports."], "keywords": ["loop", "iterate", "list", "parallelism", "aggregation", "error handling", "control flow"], "id": 18}, "UniversalConverterComponent": {"original": "Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)", "paraphrases": ["Transform data from one format to another, including JSON, CSV, String, Number, and Boolean.", "Enable the conversion of data across various types such as JSON, CSV, String, Number, and Boolean.", "Change data representations between different kinds, for instance, JSON, CSV, String, Number, and Boolean.", "Support the process of recasting data from one type to another, encompassing JSON, CSV, and primitive types.", "Translate data values and structures among diverse types like JSON, CSV, String, Number, and Boolean."], "asking": ["Could we get a node that helps us switch data from one format to another? Like, if I have a JSON object, I might need it as a CSV, or turn a string into a number or boolean.", "I frequently run into issues where my data isn't the correct type for the next operation. I need something that can reliably convert things like text to numbers, or reformat entire datasets from CSV to JSON.", "My workflow often receives data in one format but requires it in another. A node to explicitly convert between various types – like taking a plain string and making it an integer, or restructuring a JSON array into a CSV – would be incredibly helpful.", "We need a versatile data type converter. Imagine I have a column of text that should be numbers, or I get a file that's CSV but I need it as a structured JSON object. This node would handle all those format transformations.", "Is there a way to ensure my data is always the right type? Sometimes I get a 'true' or 'false' as a string, but I need it as an actual boolean. Or I might need to take an entire dataset that's currently a JSON string and parse it into a proper CSV output."], "keywords": ["convert", "converter", "data", "type", "universal", "format", "JSON", "CSV", "string", "number", "boolean"], "id": 19}, "MCP_cinematic-video-generator_generate_video": {"original": "generate and process the video", "paraphrases": ["Create and manipulate the video.", "Produce and handle the video content.", "Develop and manage the video stream.", "Fabricate and refine the video.", "Synthesize and operate on the video."], "asking": ["I need a node that can create video content and then perform various operations on it.", "Can you set up a process to generate a video and then handle its post-production or enhancements?", "I'm looking for a function that will both produce a video and then allow for its subsequent manipulation.", "My task requires the generation of a video, followed by its complete processing pipeline.", "How do I initiate video creation and then manage its processing steps?"], "keywords": ["video", "generate", "process", "cinematic", "generator"], "id": 20}, "ConditionalNode": {"original": "Evaluates multiple conditions and routes data to matching outputs", "paraphrases": ["Analyzes various criteria and directs information to the respective outputs.", "Assesses several conditions to forward data to the appropriate destinations.", "Checks multiple requirements and guides the data to its corresponding output ports.", "Determines compliance with diverse conditions, then routes the information to the suitable outputs.", "Processes a set of conditions and dispatches data to the output that matches."], "asking": ["I need a node that can act like a smart router for my data. It should be able to check multiple input conditions and then automatically send the data to the specific output that matches those conditions.", "Could we get a node that takes an input, runs it through a series of defined rules or criteria, and then dispatches it to one of many possible output paths based on which rule is satisfied?", "I'm looking for a way to implement advanced conditional logic. The node should evaluate several criteria simultaneously and direct the workflow down the appropriate branch according to the first successful match.", "We need a node that can perform multi-condition branching. It needs to assess various parameters or states and then guide the flow to the correct output based on which combination of parameters is met.", "Can you create a node that functions as a multi-way switch? It would inspect incoming data, apply a set of conditional checks, and then activate the output corresponding to the met condition."], "keywords": ["conditional", "conditions", "evaluate", "route", "logic", "decision", "control flow", "branching", "outputs"], "id": 21}, "MCP_stock-image-generation-mcp_generate_ai_stock_image": {"original": "generate and find the stock image for the video", "paraphrases": ["Source and create the stock image for the video.", "Locate or produce a stock photo for the video.", "Obtain a suitable stock visual for the video content.", "Generate and acquire a stock picture for the video project.", "Find and secure a stock graphic for the video."], "asking": ["Could you please find and provide a suitable stock image that would complement my video?", "I need to source a stock image to use in my video; can you help me generate some options or find one?", "My video requires a specific stock image. I need a function to generate or locate the best one for it.", "Let's get a stock image for this video. I need a way to both generate ideas and find the actual image.", "I'm looking for a tool or node that can help me identify and retrieve the perfect stock image to accompany my video."], "keywords": ["MCP", "stock image", "AI generation", "image generation", "video", "find"], "id": 22}, "MCP_script-generation-mcp_script_generate": {"original": "Provide topic and keyword to generator <PERSON><PERSON><PERSON>", "paraphrases": ["Supply the script generator with a topic and relevant keywords.", "Input a topic and keywords into the script generator.", "Give the script generator a topic and associated keywords.", "Feed the script generator a topic and keywords.", "Enter a topic and keywords for the script generator."], "asking": ["I need a node that represents the core service for user login, session management, and issuing secure access tokens, like JWTs.", "Can you generate a description for a microservice focused on user authentication, including details on its APIs for login, logout, and token refresh?", "I'm looking for a node describing the component responsible for verifying user credentials, supporting MFA, and handling session invalidation.", "Provide me with a node that details our system's identity management service, specifically how it authenticates users and issues tokens for further authorization.", "I want a node that outlines the 'User Authentication Service' from a technical perspective, covering its functionalities like password hashing and integration with credential stores."], "keywords": ["MCP", "script", "generation", "topic", "keyword"], "id": 23}, "MCP_DuckDuckGo_fetch_content": {"original": "\n    Fetch and parse content from a webpage URL.\n\n    Args:\n        url: The webpage URL to fetch content from\n        ctx: MCP context for logging\n    ", "paraphrases": ["Retrieves and interprets content from a specified webpage URL. It requires the `url` of the target webpage and an `ctx` parameter for MCP logging.", "This function fetches and processes data from a web address. It takes the `url` of the page to access and an `ctx` (MCP context for logging).", "Designed to extract and analyze content from a given webpage. Its inputs are the `url` of the page and an `ctx` for MCP-specific logging.", "Accesses content from a provided `url` and subsequently parses it. An `ctx` (MCP logging context) is also a required argument.", "The purpose is to obtain and make sense of content from a webpage. You must supply the target webpage `url` and an `ctx` parameter, which is an MCP context for logging."], "asking": ["I need a component that can take any given URL and intelligently pull out the main content or data from that webpage for further processing.", "Can we create a node that, when provided with a web address, goes out and retrieves all the text and relevant information from it for me?", "We're looking for a function to programmatically access a URL, download its content, and then make that content available in a structured way. Essentially, a web content extractor.", "Is there a way to implement a module that accepts a URL and returns the parsed structure or main textual body of that webpage? We need to extract data, not just download raw HTML.", "I need a utility to 'read' a webpage given its link, almost like a browser, but then process that content into a usable format for my application. Think general web content acquisition."], "keywords": ["DuckDuckGo", "fetch", "content", "parse", "webpage", "URL"], "id": 24}, "MCP_context-engine-mcp_search": {"original": "Search for documents semantically similar to a query.", "paraphrases": ["Find documents that are meaningfully similar to a search query.", "Retrieve documents exhibiting conceptual likeness to a given query.", "Locate documents whose semantic content matches a user's inquiry.", "Discover semantically related documents based on a search prompt.", "Identify documents sharing deep meaning with an input query."], "asking": ["Could we implement a feature that allows users to find documents based on their underlying meaning, rather than just exact keyword matches against a given query?", "I need a node that can take a natural language input and intelligently identify documents in our corpus that are semantically similar or conceptually relevant to it.", "We're looking for a function to perform a 'smart search' where if I ask a question or provide a phrase, it returns documents that cover the same topic, even if they use different wording.", "Can you create a component that, when given a search query, can scan through our document collection and pull out all entries that exhibit a strong semantic similarity to that query?", "My current search capabilities are too literal; I require a system where I can input a query and retrieve documents whose content is semantically aligned with my request, beyond just lexical overlap."], "keywords": ["Search", "Semantic", "Documents", "Context", "Query"], "id": 25}, "MCP_SlideSpeak_generate_powerpoint_slide_by_slide": {"original": "Generate a PowerPoint presentation slide by slide based on slides array and template using SlideSpeak", "paraphrases": ["SlideSpeak will construct a PowerPoint presentation, one slide at a time, using the provided slides array and a selected template.", "Create a PowerPoint presentation with SlideSpeak, where each individual slide is formed according to a given slides array and a chosen template.", "Leverage SlideSpeak to build a PowerPoint presentation, filling it slide by slide from an input slides array and a defined template.", "Using SlideSpeak, generate a complete PowerPoint presentation by processing a slides array and applying a specific template to each slide.", "Assemble a PowerPoint presentation slide by slide using SlideSpeak, drawing content from a slides array and formatting it with a template."], "asking": ["I'm looking for a way to programmatically assemble a PowerPoint. I've got all the content for each slide laid out in a structured array, and I need it to apply a predefined template consistently across the entire presentation using SlideSpeak.", "We need a node that can take our slide-specific data, provided as an array, and transform it into a professional PowerPoint presentation. The key is that it must generate it slide by slide and use a specified template for branding and design.", "How can I automate the creation of a .pptx file from a list of slide objects? I also need to specify a master template so that every generated slide looks consistent, ideally leveraging the SlideSpeak library for this process.", "I want to be able to dynamically build entire PowerPoint decks. I'll supply the content for each slide as an array, and the system should handle the generation, applying a designated template to ensure all slides match our visual guidelines.", "My team frequently needs to generate custom PowerPoint presentations where the content comes from an array. Is there a function or node that can iterate through this array, creating each slide and applying a common design template?"], "keywords": ["SlideSpeak", "PowerPoint", "Generate", "Presentation", "Slide-by-slide"], "id": 26}, "MergeDataComponent": {"original": "Combines multiple dictionaries or lists.", "paraphrases": ["Merges several dictionaries or lists.", "Joins various dictionaries or lists together.", "Consolidates an array of dictionaries or lists.", "Integrates diverse dictionaries or lists.", "Aggregates multiple dictionary or list structures."], "asking": ["I need a node that can take several dictionaries and lists as input and consolidate them all into a single, unified output structure.", "Is there a way to merge multiple disparate data collections, such as different dictionaries and lists, into one cohesive data object?", "I'm looking for a function that can aggregate the contents of various dictionaries and lists into one combined dictionary or list.", "How can I effectively combine a handful of separate dictionaries and lists into a single, comprehensive data structure for further processing?", "I need a tool that lets me take multiple dictionary and list inputs and seamlessly integrate their elements into a singular merged entity."], "keywords": ["merge", "data", "combine", "dictionary", "list"], "id": 27}, "MCP_Leonardo_AI_Image_Generator_generateHeroImage": {"original": "Generate a hero image from text", "paraphrases": ["Create a prominent banner image from text.", "Design a hero graphic based on textual input.", "Synthesize a main header visual using text.", "Produce an impactful feature image from written content.", "Derive a top-of-page graphic solely from text data."], "asking": ["I need a way to automatically generate a compelling hero image for my articles or web pages, simply by providing a textual description.", "Could we have a node that creates a prominent main banner graphic for our marketing content, just from a brief text prompt?", "I'm looking for a function that can produce a striking, large-format visual—a hero image—based entirely on a textual input.", "We need a tool that can quickly design a prominent header image for our landing pages, just by describing what we want in text.", "Is there a component that takes a textual concept and translates it into a high-impact, primary visual suitable for a hero section?"], "keywords": ["AI", "Image Generation", "Text-to-Image", "Hero Image", "Leonardo AI", "Visual Content", "Content Creation"], "id": 28}, "AlterMetadataComponent": {"original": "Modifies metadata dictionary keys.", "paraphrases": ["Alters the keys of the metadata dictionary.", "Changes the names of the metadata dictionary's keys.", "Revises the labels associated with metadata dictionary entries.", "Edits the identifiers used as keys in the metadata dictionary.", "Adjusts the key names within the metadata dictionary."], "asking": ["I need a way to rename the existing keys within my metadata dictionary. For example, if I have 'project_id', I want to change it to 'id' for consistency.", "Could we have a node that allows us to standardize the naming of metadata keys? Often, they come in with different casing or slight variations, and I need to make them all conform to a single convention.", "I'm looking for a node that can take an input metadata dictionary and transform its keys based on a predefined mapping. So, 'old_key_1' becomes 'new_key_A', and so on.", "We've got some awkwardly named keys in our metadata, and I'd like to refactor them for better clarity. Is there a component that lets me easily update the names of the keys without altering their associated values?", "Before I pass this metadata to the next stage, I need to ensure its dictionary keys match the expected schema. Can we get a node that can modify or adjust the names of the metadata dictionary keys to fit specific requirements?"], "keywords": ["metadata", "alter", "dictionary", "keys", "component"], "id": 29}, "MCP_Redis-mcp-01_set": {"original": "Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n", "paraphrases": ["This function enables you to store a string value in Redis using a specified key, optionally setting an expiration time in seconds, and will return a status message.", "You can save a string to <PERSON><PERSON> with an associated key and an optional duration (in seconds) after which it will expire. The function provides a string indicating success or failure.", "To set a Redis string, provide a key and its value. You may also specify a time-to-live in seconds. A confirmation or error message string is returned.", "Establishes a string entry in Redis identified by a key, allowing for an optional expiration period defined in seconds. The operation yields a descriptive string message.", "Persist a string data element in Redis under a given key. An optional integer representing seconds can be used to set its expiry. The function concludes by returning a textual status report."], "asking": ["I'm looking for a node that simply lets me set a string value in Red<PERSON> using a specified key, and it would be great if I could also set an optional expiry for it.", "Could you create a function where I can store a string in Redis under a specific key, with the added capability to define an expiration time in seconds?", "I need a tool that can take a key and a string value, then write that to Redis, and it absolutely needs to support an optional time-to-live setting.", "My requirement is for a component that allows me to write arbitrary string data to a Redis key, and I need the flexibility to make that data expire after a certain duration.", "Please develop a node that provides the functionality to set a Redis string for a given key, with an optional parameter to control its automatic expiration."], "keywords": ["Redis", "Set", "String", "Expiration", "MCP"], "id": 30}, "MCP_Redis-mcp-01_get": {"original": "Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n", "paraphrases": ["Fetch the string value associated with a given key from Redis.", "Retrieve a string from Redis based on its key, returning the value or an error message.", "Obtain the string content stored in Redis using the provided key, or an error if unsuccessful.", "This operation retrieves a string value from Redis, identified by a specific key, and returns either the value or an error.", "Accesses a Redis string using its key, providing the stored value or an indication of an error."], "asking": ["I need a node that can fetch a string value from Red<PERSON>, given a specific key. How can I get that implemented?", "Could you provide a component or function that allows me to retrieve a string from Redis by passing in its key?", "My workflow requires reading a string value from Redis. Is there a way to input a key and get the corresponding string back?", "I'm looking for a utility to pull a string out of Redis. Essentially, I'll provide the key, and it should return the stored string or an error.", "How do I access a string value in Red<PERSON> using its key? I need a simple node for retrieving that data."], "keywords": ["Redis", "Get", "String", "Value", "Key", "MCP"], "id": 31}, "MCP_Eraser_Diagram_Generator_generateDiagram": {"original": "Generate a diagram from text description using Eraser API", "paraphrases": ["Create diagrams from textual input using the Eraser API.", "Produce a diagram based on a text description through the Eraser API.", "The Eraser API enables generating diagrams from text descriptions.", "Convert text descriptions into diagrams with the Eraser API.", "Utilize the Eraser API to visualize text descriptions as diagrams."], "asking": ["I need a component that can take some plain text and automatically convert it into a visual diagram or flowchart using the Eraser API. Basically, I want to describe a system and have it draw it for me.", "Could you create a node that leverages Eraser.io to turn my descriptive text inputs into graphical representations? I'm looking for an easy way to generate diagrams directly from my workflow.", "I'm trying to find a way to programmatically generate diagrams from textual prompts. Is there a node available, or can one be built, that interfaces with the Eraser API for this purpose?", "My team often needs to quickly visualize concepts and processes. We'd love a tool or a node that lets us type out our ideas and get a diagram back, similar to what Eraser API offers.", "We're looking to integrate diagram generation into our automated documentation pipeline. We need a node that takes a given text description and uses the Eraser API to render a corresponding diagram, which we can then embed."], "keywords": ["Diagram Generation", "Eraser API", "Text-to-Diagram"], "id": 32}, "MCP_Content_Management_System_create_webflow_blog_post": {"original": "Create a blog post in Webflow. This tool automatically analyzes the blog post content, creates the necessary collection fields if they don't exist, and posts the blog content. No need to specify collection fields - they are generated automatically based on the blog post data.", "paraphrases": ["Effortlessly publish a blog post to Webflow; the system automatically analyzes your content, generates any required collection fields, and then posts the blog, eliminating manual field setup.", "When creating a blog post in Webflow, the tool intelligently processes your content, automatically establishing necessary collection fields if they don't exist, and then publishes the post, requiring no manual field definition.", "Posting a blog on Webflow is simplified: the platform automatically analyzes your content, creates all essential collection fields based on the post data, and then publishes it without any need for you to specify them.", "This Webflow feature streamlines blog publishing by automatically analyzing content, creating new collection fields as needed, and posting the blog, meaning users don't have to define fields themselves.", "You can publish a blog post in Webflow with ease, as the system automatically handles content analysis, creates the appropriate collection fields from the post's data, and publishes the blog without requiring manual field specification."], "asking": ["I need a node that can take my blog content and publish it directly to Webflow. The important part is that I don't want to define any collection fields; it should just figure out what fields are needed from the content itself and create them automatically before posting.", "Can you build a tool that accepts blog post data and pushes it to Webflow? I'm looking for something intelligent enough to analyze the content, automatically create the necessary collection fields in Webflow if they're missing, and then publish the post without me having to specify field names.", "My goal is to simplify Webflow blog publishing. I want to feed it a blog post, and it should automatically parse the content, generate any required collection fields in Webflow if they don't already exist, and then add the new blog entry. So, I just provide the content, and it handles the Webflow structure.", "I'm looking for an automation that will publish a blog post to Webflow. The key feature is that it needs to dynamically create Webflow collection fields based on the content I provide. I don't want to pre-configure fields; it should analyze the blog post, create relevant fields automatically, and then post it.", "I need a node that automates blog posting to Webflow from raw content. It should be smart enough to inspect the content, determine what collection fields are appropriate, create those fields in Webflow if they're not there, and then publish the blog post without any manual setup of the collection schema."], "keywords": ["MCP", "CMS", "Webflow", "Blog Post", "Create", "Post", "Automation"], "id": 33}, "MCP_Website_Generator_repo_setup": {"original": "\n    Setup a new repository for a website project by cloning a template, creating a GitHub repo, and pushing the code.\n\n    This tool performs the following steps:\n    1. <PERSON>lone the React Vite template repository directly to base directory\n    2. Remove the .git folder from the cloned repository\n    3. Create a new repository on GitHub with the given project name\n    4. Push the code to the new GitHub repository\n    5. Optionally deploy to AWS Amplify (if requested)\n\n    Args:\n        project_name: Name of the project and GitHub repository\n        description: Optional description for the GitHub repository\n        deploy_to_amplify: Whether to deploy to AWS Amplify (optional, not implemented yet)\n\n    Returns:\n        Status message with repository URL and deployment information\n    ", "paraphrases": ["Automates the setup of a new web project, including cloning a template, creating a GitHub repository, and pushing the initial code.", "A utility that streamlines the process of initializing a website project by cloning a template, creating a new GitHub repository, and pushing the code to it.", "This tool initializes a new website project repository from a cloned template, establishes a dedicated GitHub repository, and pushes the initial code, with an option for AWS Amplify deployment.", "Simplifies the creation of a new web project by handling the cloning of a React Vite template, the setup of a new GitHub repository, the pushing of code, and optionally deploying to AWS Amplify.", "Creates a new website project workspace from a template, sets up a new GitHub repository on your account, pushes the template code, and offers an option for AWS Amplify deployment."], "asking": ["I need to kickstart a new website project. Can you take care of cloning a React Vite template, setting up a new GitHub repository for it, and pushing the initial code there for me?", "Could you help me create a new web application repository? I'm looking for a solution that clones the React Vite template, establishes a new GitHub repo with a given project name, and pushes the code, with an option for AWS Amplify deployment.", "I'm looking to quickly set up a fresh web project. Is there a tool that automates cloning a React Vite template, creating a new GitHub repository, pushing the code, and potentially handling deployment to AWS Amplify?", "I need to initialize a new React Vite project, get it version-controlled on GitHub, and have the initial code pushed. Is there a way to streamline this entire process, including an optional deployment step?", "Can you create a new GitHub repository for a web project, populate it with a React Vite template, and push the initial code? Also, I'd like the option to deploy this project to AWS Amplify."], "keywords": ["website generation", "repository setup", "GitHub", "React Vite", "AWS Amplify", "project creation", "code deployment", "template cloning"], "id": 34}, "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-extract": {"original": "A powerful web content extraction tool that retrieves and processes raw content from specified URLs, ideal for data collection, content analysis, and research tasks.", "paraphrases": ["This robust web content extractor efficiently retrieves and processes raw data from specified URLs, perfect for data collection, content analysis, and research.", "An advanced tool for gathering and processing raw web content from designated internet addresses, well-suited for data acquisition, content evaluation, and research projects.", "Ideal for data collection, content analysis, and research tasks, this powerful utility extracts and refines unprocessed information directly from target URLs.", "A sophisticated system designed to pull and handle raw content from given URLs, proving highly valuable for compiling data, analyzing web material, and conducting studies.", "This potent software retrieves and prepares original web content from specific URLs, offering significant utility for data compilation, content scrutiny, and various research endeavors."], "asking": ["I need a node that can reliably fetch and process the raw content from specific web addresses, which would be invaluable for my data collection and research tasks.", "Could you build a tool that allows me to input URLs and then extracts all the raw information from those pages, perfect for our content analysis pipeline?", "We're looking for a module that specializes in retrieving and making available the raw textual and structural content from a given URL for various analytical and data-gathering purposes.", "I require a component that can systematically pull raw web page content from a list of URLs, essential for my deep-dive research and data acquisition projects.", "Please create a node capable of accessing and extracting the full raw content from specified URLs, as it's critical for our data mining and content processing initiatives."], "keywords": ["<PERSON><PERSON>", "Web Extraction", "Content Extraction", "Data Collection", "Content Analysis", "Research", "URL Processing", "Web Search"], "id": 35}, "MCP_Website_Generator_create_file": {"original": "\n    Create a new file in the project with content generated by the AI Agent.\n\n    This tool performs the following steps:\n    1. Get the file_name, file_path and content from the AI Agent\n    2. Create a new file with the given file_name at the file_path\n    3. Write the content in the file\n\n    Args:\n        file_name: Name of the file to create (e.g., \"index.html\", \"app.js\")\n        file_path: Directory path where the file should be created (e.g., \"./src\", \"./public\")\n        content: Content to write to the file\n\n    Returns:\n        Status message indicating success or failure\n    ", "paraphrases": ["Instruct the AI Agent to generate content and then create a new file, specifying its name and location within the project, before writing the AI-generated text into it.", "This utility enables an AI Agent to provision a new file in the project by providing the file's name, directory path, and the content to be written.", "The process involves an AI Agent supplying a filename, a target path, and the content, which is then used to construct a new file at that location and populate it with the given text.", "Leveraging this tool, an AI Agent can establish a new file within the project at a defined path and with a specific name, subsequently filling it with AI-generated content.", "Facilitate the creation of a project file, named and located according to AI Agent specifications, and populated with AI-generated text."], "asking": ["I need a way to create a brand new file in the project, specifying its name, where it should be located, and what content it should contain.", "Can you generate a new file for me, place it in a specific directory path, give it a particular name, and then write the provided content into it?", "I'd like to add a file to the project structure; I'll give you the desired filename, its target directory, and the text that needs to go inside.", "My goal is to instantiate a new file artifact within the project, defining its full path (directory and name) and the content it should hold.", "Please provide a function to create a file in the project, allowing me to specify both the filename, the directory path for its creation, and the content to populate it with."], "keywords": ["MCP", "Website Generator", "AI Agent", "create file", "write content"], "id": 36}, "MCP_Website_Generator_push_changes": {"original": "\n    Commit the changes made to the project and push them to the remote repository.\n\n    This tool performs the following steps:\n    1. Get the project name from the AI Agent\n    2. Locate the project directory in the base directory\n    3. Check if there are any changes made to the project\n    4. If there are no changes, it will return\n    5. If there are changes, it will stage the changes\n    6. Commit the changes with an auto-generated commit message\n    7. Push the changes to the remote repository\n\n    Args:\n        project_name: Name of the project (same as used in repo_setup)\n\n    Returns:\n        Status message indicating success or failure\n    ", "paraphrases": ["This tool facilitates the version control process by first obtaining the project name from an AI Agent, then locating its directory. It checks for changes, stages them if present, commits them with an auto-generated message, and finally pushes them to the remote repository. If no changes are detected, the process concludes without action.", "To update the remote repository, this utility performs several steps: it retrieves the project name, finds the corresponding directory, identifies any modifications, stages them, generates a commit message and commits them, and then pushes these changes. A status message is returned upon completion, and the tool will return immediately if no changes are found.", "This function is designed to commit all modifications made to a specified project and push them to its remote repository. It begins by getting the project name, locating its files, and then conditionally stages, commits (with an auto-generated message), and pushes any detected changes. If no changes are present, the operation stops.", "Executing a comprehensive update, this script acquires the project's name, navigates to its directory, and assesses for changes. If modifications exist, they are staged, committed using an automatically generated message, and subsequently pushed to the remote repository. Otherwise, the process terminates.", "Manage the synchronization of project updates: this system obtains the project name, identifies its local directory, verifies for changes, stages any detected changes, commits them using a system-generated message, and pushes them to the remote repository. It provides a status message indicating success or failure, or concludes if no changes are found."], "asking": ["I need a tool that can automatically commit all the changes made to a project and then push them to the remote repository. It should handle staging and even generate a commit message for me.", "Could you create a node that finalizes all current work in a specified project, commits those changes, and ensures they're uploaded to the remote repo? I'd just provide the project name.", "I'm looking for a function that takes a project name, checks for any modifications, and if found, stages them, commits with a default message, and pushes everything upstream.", "I need a way to wrap up my project changes; something that will commit everything that's been altered and then push those commits to the cloud. Basically, a 'save and send' feature for my codebase.", "Can we get a utility that automates the whole commit-and-push process for a given project? It should find the project, stage any new changes, commit them, and then push them to the corresponding remote."], "keywords": ["MCP", "Website Generator", "commit", "push", "git", "version control", "repository", "project changes", "auto-commit"], "id": 37}, "MCP_Website_Generator_list_files": {"original": "\n    List all files and directories in the specified directory.\n\n    This tool provides a directory listing showing files and subdirectories.\n    Files are marked with [FILE] and directories with [DIR] prefixes.\n\n    Args:\n        directory_path: Path to the directory to list (can be relative or absolute)\n\n    Returns:\n        Directory listing or error message\n    ", "paraphrases": ["Retrieve a comprehensive list of items, including files and subdirectories, from a specified path, with each item clearly identified by its type.", "This utility generates a report of a designated directory's contents, distinctly labeling each entry as either a file or a folder.", "Display the contents of a directory, specifying the type (file or directory) for each item found at the provided path.", "Obtain a listing of a directory's contents, where each file is prefixed with [FILE] and each subdirectory with [DIR], for a given path.", "Enumerate the elements within a specified directory path, indicating whether each element is a file or a subdirectory in the output."], "asking": ["I need a way to inspect the contents of a directory. Can you show me all the files and folders within a specific path?", "Is there a tool that can provide a detailed list of what's inside a particular folder? I'd like to see both files and any subdirectories it contains.", "My goal is to get a complete overview of a directory's structure. I need a feature that can list all its files and subdirectories, maybe even marking them clearly.", "I'm trying to figure out what's stored in a certain location. Could you provide a listing that includes all the files and subfolders in that directory?", "I require functionality to obtain an enumeration of the entities present within a specified directory, with an explicit indication of whether each entity is a file or another directory."], "keywords": ["list_files", "directory_listing", "files", "directories", "MCP", "Website_Generator"], "id": 38}, "MCP_Website_Generator_read_file": {"original": "\n    Read the contents of a file.\n\n    This tool allows reading the contents of any file in the project or system.\n    Useful for examining configuration files, source code, or any text-based files.\n\n    Args:\n        file_path: Path to the file to read (can be relative or absolute)\n\n    Returns:\n        File contents or error message\n    ", "paraphrases": ["This tool is used to retrieve the contents of any specified file, whether its path is relative or absolute, and is useful for inspecting text-based files such as configuration files or source code, returning the file's data or an error.", "Access the data within any file in your project or system using this utility. Simply provide the file's path, and it will return its content or an appropriate error message, proving valuable for reviewing code or configurations.", "Designed for reading the contents of files, this function accepts a file path (relative or absolute) and returns the text found within it, or an error if the operation fails. It's particularly helpful for examining source code and configuration files.", "Obtain the full text content of a file by specifying its path with this tool. It supports reading any file across the project or system and is highly effective for inspecting various text documents like software source or system configurations.", "With this feature, you can read and display the text from any file by providing its path. It can handle both relative and absolute paths, making it convenient for viewing configuration files, source code, and other text documents within the project or system, outputting the content or an error."], "asking": ["I need a node that can take a file path and then just give me all the text content from that file. Think of it as peeking inside a file to see what's written there.", "Can we implement a tool that allows us to extract the entire textual data from any file, given its location? This would be super useful for reviewing configuration files or logs.", "We need a component that can retrieve the contents of a specified file. The input would be the file's path, and the output would be the text within that file, for analysis or display.", "I'm looking for a way to simply read the text out of a file. So, I provide a path to a file, and it returns the whole document's content.", "Is there a node or command to get the full content of a file, like if I just point to it by its path? I need to be able to see everything that's stored in a particular file."], "keywords": ["MCP", "Website Generator", "read", "file", "content", "project", "system", "configuration", "source code", "text"], "id": 39}, "MCP_Google_Sheets_find_row": {"original": "Find one or more rows by a column and value", "paraphrases": ["Retrieve records that have a specific value in a designated column.", "Locate data rows where a particular field contains a given entry.", "Filter rows based on the content of a specified column.", "Search for entries that match a certain value within a chosen attribute.", "Identify one or more rows by comparing a column's content to a value."], "asking": ["I need a node that can filter a dataset to return all entries where a specified column's value matches a given input.", "Can you provide a component that allows me to retrieve records from a table by performing a lookup on a particular column and its corresponding value?", "I'm looking for a function that lets me query a collection of items, extracting all of them that have a specific value in a designated field.", "How can I select one or more rows from my data where the content of a certain column is exactly equal to a value I provide?", "Please create a node that enables me to extract data by matching a specific value within a chosen column, bringing back all relevant rows."], "keywords": ["MCP", "Google Sheets", "find", "row", "search", "retrieve", "column", "value"], "id": 40}, "MCP_Google_Sheets_update_cell": {"original": "Update a cell in a spreadsheet", "paraphrases": ["Modify a specific cell within a spreadsheet.", "Change the value of a spreadsheet cell.", "Edit data in a spreadsheet cell.", "Alter the content of a cell in an electronic ledger.", "Make adjustments to a spreadsheet's cell entry."], "asking": ["I need to change the value of a specific cell within a spreadsheet.", "Can you create a feature that allows me to modify the data in a particular spreadsheet cell?", "I'm looking for a way to just overwrite the content of one cell in my sheet, not the whole row or column.", "Provide a mechanism to perform a targeted update on a single cell's value in a given spreadsheet.", "My goal is to input new data into an existing cell location in a spreadsheet, effectively replacing its current content."], "keywords": ["Google Sheets", "Spreadsheet", "Update", "Cell"], "id": 41}, "MCP_Google_Sheets_get_cell": {"original": "Fetch the contents of a specific cell in a spreadsheet", "paraphrases": ["Retrieve the value from a designated spreadsheet cell.", "Extract data located in a particular cell within a spreadsheet.", "Obtain the information held in a specified spreadsheet cell.", "Access the content of an exact cell in a spreadsheet document.", "Get the datum from one specific cell of a digital ledger."], "asking": ["I need a node that can extract the value from a specific cell, like 'A1' or 'B7', within a given spreadsheet. How can I achieve that?", "How do I read the data that's stored in a particular cell address in a spreadsheet, for instance, to get the content of cell 'C5'?", "My workflow requires pulling just one piece of information directly from a designated cell in a spreadsheet. Can you create a node for retrieving a specific cell's content?", "I'm looking for a way to access and return the value held within a precise cell reference, like 'D2', in any spreadsheet. Is there a node that performs this action?", "I have a spreadsheet and I want to get the data that's located in a known cell. Can you provide a node to fetch the content of a single, specified cell?"], "keywords": ["Google Sheets", "Cell", "Get", "<PERSON>tch", "Spreadsheet"], "id": 42}, "MCP_Google_Document_create_document": {"original": "Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats.", "paraphrases": ["Generate a new Google Document, optionally including a title and content. It accepts input in plain text, HTML, and Markdown.", "You can create a fresh Google Doc, with the choice to add a title and body, and it supports plain text, HTML, or Markdown formats.", "Initiate a Google Document; a title and content can be provided but are not mandatory. Supported content types are plain text, HTML, and Markdown.", "A new Google Doc can be created, allowing for an optional title and content. It's compatible with plain text, HTML, and Markdown formats.", "This feature enables the creation of a new Google Document, where a title and content are optional, and input can be in plain text, HTML, or Markdown."], "asking": ["I need a way to generate a fresh Google Document, and I'd like the flexibility to include a title and populate it with content, whether that content is simple text, HTML, or even Markdown.", "Can you create a component that lets me make a brand new Google Doc? It should allow me to set a title if I want, and importantly, accept content in plain text, HTML, or Markdown format.", "I'm looking for a node that can instantiate a new Google Document. This node should support providing an optional title and be able to ingest content in multiple formats, specifically plain text, HTML, and Markdown.", "We need to be able to programmatically create Google Docs. The key features are being able to specify an optional title and provide the document's body content, which might come in as raw text, HTML markup, or Markdown syntax.", "My use case requires creating a new Google Doc and inserting content that could be formatted in different ways – sometimes just plain text, other times HTML, and even Markdown. It would also be great to be able to add an optional title."], "keywords": ["Google", "Document", "create", "content", "text", "HTML", "<PERSON><PERSON>"], "id": 43}, "MCP_Google_Sheets_create_worksheet": {"original": "Create a blank worksheet with a title", "paraphrases": ["Make an empty spreadsheet and assign it a name.", "Generate a new, blank worksheet that includes a title.", "Set up a fresh, unpopulated sheet and give it a heading.", "Open a new, blank document and then add a title.", "Initiate a clean worksheet and provide a name for it."], "asking": ["Could you please create an empty worksheet for me, and make sure there's a place to put a title on it?", "I need to start a new spreadsheet document, and I want to give it a name right from the beginning.", "My task requires generating a fresh worksheet that allows me to immediately add a title.", "Let's set up a new, blank worksheet, ensuring it's titled upon creation.", "I'm looking to open a brand new, empty workbook page and assign it a specific name."], "keywords": ["Google Sheets", "create", "worksheet", "sheet"], "id": 44}, "MCP_Google_Sheets_add_single_row": {"original": "Add a single row of data to Google Sheets", "paraphrases": ["Input one data row into Google Sheets.", "Insert a single line of information into your Google spreadsheet.", "Append one new record to a Google Sheets document.", "Enter just one row of data into the Google Sheets application.", "Place a solitary data entry into a Google Sheet."], "asking": ["I need to append a new row of data to a Google Sheet.", "Is there a node available to insert just one line of information into Google Sheets?", "My workflow requires adding a single record to a specified Google Sheet.", "I want to log new data as a fresh row in a Google spreadsheet.", "Could we implement a function to write one row of data into a Google Sheet?"], "keywords": ["Google Sheets", "add", "single row", "row", "data", "MCP"], "id": 45}, "IDGeneratorComponent": {"original": "Generates various types of unique identifiers (UUID, timestamp, short ID).", "paraphrases": ["Produces diverse unique identifiers, including UUIDs, timestamps, and short IDs.", "Creates different forms of unique identification codes, such as UUIDs, time-based values, and brief IDs.", "Outputs a variety of distinct identifiers: UUIDs, timestamps, and compact IDs.", "Generates several types of unique IDs, like UUIDs, time stamps, and short-form identifiers.", "Develops various unique codes for identification, such as UUIDs, time-sensitive IDs, and concise IDs."], "asking": ["I'm looking for a node that can create different kinds of unique identifiers – standard UUIDs, short IDs, and timestamp-based ones.", "My project needs a robust ID generation system. I'll need UUIDs for global uniqueness, but also shorter, more readable IDs, and sometimes timestamp-encoded identifiers. Can you point me to a node for this?", "Could we get a node that generates unique IDs? I need it to support UUIDs, but also options for short, custom IDs and IDs derived from the current timestamp.", "I need a tool to generate various types of unique IDs. Specifically, I'm after something that can give me UUIDs, compact short IDs, and identifiers based on the current time.", "We need a utility that provides different unique identifier formats: a standard UUID, a concise short ID, and an ID that incorporates a timestamp. Is there a node that offers all these capabilities?"], "keywords": ["ID Generator", "Identifier", "Unique", "UUID", "Timestamp", "Short ID"], "id": 46}, "MCP_Google_Forms_create_google_form": {"original": "Create a new Google Form", "paraphrases": ["Generate a new Google Form.", "Set up a new Google Form.", "Build a fresh Google Form.", "Start a new Google Form.", "Initiate the creation of a Google Form."], "asking": ["I need you to set up a new Google Form for me, please.", "Could you create a fresh Google Form for my needs?", "Please generate a new Google Form.", "I'm looking to have a new Google Form made.", "Go ahead and make a new Google Form, will you?"], "keywords": ["Google Forms", "create", "form", "Google"], "id": 47}, "MCP_Google_Forms_update_google_form": {"original": "Update a Google Form with new questions", "paraphrases": ["Add new questions to a Google Form.", "Modify a Google Form by adding more questions.", "Revise a Google Form to include fresh inquiries.", "Incorporate additional questions into an existing Google Form.", "Update a Google Form's content with new queries."], "asking": ["I need to dynamically add questions to one of my Google Forms.", "Can you help me update a Google Form by inserting new questions?", "I'm looking for a way to expand an existing Google Form with a new set of questions.", "When new survey items become available, I want them automatically reflected in my Google Form's questions.", "I need to modify a Google Form's content to include recently defined questions."], "keywords": ["Google Forms", "Update", "questions", "form"], "id": 48}, "MCP_Google_Document_update_document": {"original": "Update a Google Document with new content at a specific position", "paraphrases": ["Insert new material into a Google Doc at a designated spot.", "Modify a Google Document by adding content to a precise location.", "Append fresh information to an exact point within a Google Document.", "Inject new data into a specific section of a Google Document.", "Revise a Google Doc by incorporating new content at a predefined position."], "asking": ["I need a node that can insert new text into a Google Document at a user-defined position, not just append it.", "Can you create a feature that allows me to modify content within an existing Google Doc, specifically targeting and replacing text at a given index or section?", "I'm looking for a way to programmatically update a Google Doc by adding or changing content at a precise location within the document's structure.", "We need a function that enables us to inject specific information into a Google Doc at a designated spot, like after a particular heading or paragraph.", "I need a node that updates a Google Doc by inserting new material at a specified offset or position within the document's content."], "keywords": ["Google Document", "Update", "Content", "Position"], "id": 49}, "MCP_Google_Document_append_document": {"original": "Append content to the end of a Google Document", "paraphrases": ["Add content to the conclusion of a Google Document.", "Extend an existing Google Doc with new material.", "Insert information at the final part of a Google Document.", "Place additional content at the very end of a Google Document.", "Supplement a Google Document by adding content at its close."], "asking": ["I need to add some new information to the very end of an existing Google Document.", "Could you build a step that extends a Google Document by adding content to its conclusion?", "I'm looking for a way to automatically tack on additional text or data at the bottom of a specific Google Doc.", "My process requires a node that can append a block of content to the end of a Google Document.", "I need to insert some new text at the final position within an existing Google Document."], "keywords": ["Google Document", "append", "add", "content", "document", "Google"], "id": 50}, "MCP_Gmail_send_email": {"original": "Create and send a new email message", "paraphrases": ["Draft and dispatch an email.", "Compose and transmit an electronic mail.", "Generate and forward a new email.", "Write and send an email communication.", "Initiate and dispatch an email message."], "asking": ["I need a node that allows me to compose and dispatch a new email.", "Can you create a feature for generating and sending an email message?", "My workflow requires a step to put together an email and then send it.", "I'm looking for a capability to initiate a new email transmission.", "Could you add the functionality to create an email and ensure its delivery?"], "keywords": ["MCP", "Gmail", "send", "email", "create"], "id": 51}, "MCP_MetaAds_create_meta_campaign": {"original": "Create a Meta Ads campaign with specified configuration", "paraphrases": ["Establish a Meta advertising campaign following predefined settings.", "Set up a Meta Ads initiative using particular parameters.", "Develop a Meta-platform advertising effort with designated configurations.", "Initiate a Meta Ads campaign in accordance with predetermined specifications.", "Launch a Meta advertisement campaign based on provided configurations."], "asking": ["I need to set up a brand new advertising campaign on Meta, and I'll be providing all the specific settings and details for its configuration.", "Could you create a Meta ad campaign for me? I have a precise configuration ready that I want to use.", "I'm looking to initiate a Facebook and Instagram ad campaign, complete with all the custom parameters I've defined.", "I want to launch an ad campaign on Meta platforms, and I need to be able to specify every aspect of its setup.", "Can you provide a way to generate a Meta advertising campaign, allowing me to input all the necessary configuration details?"], "keywords": ["Meta Ads", "Campaign", "Create", "Advertising", "Meta", "Ads"], "id": 52}, "MCP_MetaAds_create_adset": {"original": "Create a new ad set in a Meta Ads account", "paraphrases": ["Establish a fresh ad set within a Meta advertising platform.", "Set up an additional ad set inside your Meta Ads manager.", "Generate a brand-new ad set within the Meta advertising environment.", "Initiate the creation of an ad set in a Meta Ads profile.", "Build a new ad set on a Meta advertising account."], "asking": ["I need to get a new ad set configured within my Meta Ads account.", "Could you create an ad set for me on Meta?", "Let's set up a new ad set in Meta Ads.", "I'm looking to define the parameters for a fresh ad set on the Meta platform.", "Time to build out another ad set in Meta."], "keywords": ["MetaAds", "create", "adset", "Meta", "advertising"], "id": 53}, "MCP_MetaAds_upload_ad_image": {"original": "Upload an image to use in Meta Ads creatives", "paraphrases": ["Provide a picture for your Meta ad campaigns.", "Submit a photo for use in Meta advertising visuals.", "Choose and add an image for your Meta promotional content.", "Post a graphic for your Meta ad designs.", "Include an image for creating Meta's advertising material."], "asking": ["I need a way to upload an image that I can then use in my Meta Ads creatives.", "How do I get a picture into my Facebook or Instagram ad campaigns for the creative part?", "I'm trying to add a visual element to a Meta Ad, what's the step for uploading the image file?", "Could you provide a function or node that allows me to put an image into the Meta Ads creative builder?", "Please enable the capability to upload a visual asset specifically designated for Meta advertising creatives."], "keywords": ["Meta Ads", "Upload", "Ad Image", "Creatives"], "id": 54}, "MCP_MetaAds_create_ad_creative": {"original": "Create a new ad creative using an uploaded image hash", "paraphrases": ["Generate a fresh advertising creative from an existing image hash.", "Produce a novel advertisement by referencing an uploaded image hash.", "Develop an original ad asset using an image hash that has been provided.", "Formulate a unique advertising piece based on a previously uploaded image hash.", "Design a brand-new ad creative, drawing from an image hash that's already in the system."], "asking": ["I need to create a new ad, and I want to use an image that's already been uploaded by referencing its unique hash.", "Can you help me generate an ad creative, specifically by taking an existing uploaded image hash as input?", "Please provide a function to instantiate a new ad creative object, utilizing an uploaded image identified by its hash.", "I've got an image already uploaded to the system; how do I convert that into a new ad creative using its hash?", "I'm looking for a way to construct an ad creative where the visual element is linked directly via an uploaded image's hash."], "keywords": ["MetaAds", "ad creative", "create", "image hash"], "id": 55}, "MCP_MetaAds_create_ad": {"original": "Create a new ad with an existing creative", "paraphrases": ["Develop an advertisement using pre-existing creative assets.", "Generate a fresh advertising campaign employing existing visual or textual material.", "Design a novel ad by leveraging an already-made creative.", "Produce an advertisement from scratch, incorporating an available creative element.", "Formulate a new promotional piece utilizing existing creative content."], "asking": ["I need to make a new advertisement, but I want to use a creative I've already designed.", "Can you help me set up an ad by reusing an existing creative asset?", "Please create a new ad where the creative component is pulled from something already in our library.", "I'm looking to launch another ad; I'd like to use a creative that's already been uploaded.", "Generate a new ad campaign, linking it to a creative that is already available in the system."], "keywords": ["MetaAds", "create", "ad", "creative"], "id": 56}, "workflow-3c17973e-646f-48a8-b84c-eb65f37646b5": {"original": "Profile_Research_Workflow", "paraphrases": ["The process of researching user profiles", "Steps for conducting profile-based research", "Workflow for user profile analysis in research", "Methodology for researching and profiling", "The research journey for user profiles"], "asking": ["I need a clearly defined sequence of steps for how we gather information and build out profiles. Can we get a node that outlines our official profile research process?", "We frequently need to generate detailed profiles, and I'd like a standardized system or a dedicated node that helps us manage that entire research and creation lifecycle.", "Our current profile research is a bit ad-hoc. I'm looking for a way to standardize and streamline how we conduct research and compile profiles. Could we set up a 'profile research workflow' node?", "To ensure consistency, I need a blueprint for how we conduct data collection, analysis, and synthesis when constructing new profiles. I'm thinking of a workflow node specifically for profile research.", "We're constantly creating new profiles and need a systematic approach. Can you create a node that encompasses the end-to-end process for researching and developing these profiles, from initial data pull to final output?"], "keywords": ["Profile", "Research", "Workflow", "3c17973e-646f-48a8-b84c-eb65f37646b5"], "id": 57}, "workflow-d3ec861d-da29-4e5e-8c8e-db480d15d5cd": {"original": "Company_Research_Workflow", "paraphrases": ["Corporate Investigation Procedure", "Business Study Process Flow", "Enterprise Research Protocol", "Company Analysis Process", "Organizational Research Methodology"], "asking": ["Could you create a structured process or a module that handles all the steps involved in researching a company?", "I'm looking for a way to automate and standardize how we gather information and perform due diligence on target companies.", "We need a comprehensive workflow built out specifically for conducting in-depth research on any given company.", "Can you set up a dedicated system or a sequence of tasks that guides us through our company research from start to finish?", "My team needs a robust solution that encapsulates the entire methodology for investigating companies effectively and consistently."], "keywords": ["workflow", "Company", "Research", "Company Research", "d3ec861d-da29-4e5e-8c8e-db480d15d5cd"], "id": 58}, "workflow-cf07c857-008c-44a7-b164-07f40cee8461": {"original": "Research_Workflow", "paraphrases": ["The sequence of research activities", "The process of conducting research", "The methodology for academic investigation", "The structured approach to research tasks", "The operational flow of a research project"], "asking": ["Could you create a node that details the entire process for conducting research, from initial concept to conclusion?", "I'm looking for a way to structure and streamline my research projects; can you provide a node defining a typical 'Research_Workflow'?", "I need a comprehensive outline of the steps and sequence involved in a research endeavor. Can you generate a 'Research_Workflow' node for that?", "Please provide a description for a standard methodology or sequence of operations for a research project. I'm imagining a node called 'Research_Workflow'.", "I'd like to request a node that captures the typical stages and flow of any research initiative. Essentially, I need a 'Research_Workflow' defined."], "keywords": ["workflow", "research", "cf07c857-008c-44a7-b164-07f40cee8461"], "id": 59}, "MCP_Leonardo_MCP_generateHeroImage": {"original": "Generate a hero image from text", "paraphrases": ["Create a hero image using text input.", "Produce a prominent banner graphic from text.", "Design a large header visual based on written input.", "Synthesize a main marketing image from textual content.", "Develop a leading visual asset from descriptive text."], "asking": ["I need a way to create a prominent banner image, like a hero shot, by simply feeding it a text description.", "Could you generate an eye-catching main visual for my page based on a short text prompt I provide?", "I'm looking for a node that takes text input and then outputs a compelling hero image.", "How can I automatically produce a primary graphic for my content using just a textual concept?", "Can you help me get a powerful header image created from a text-based idea I have in mind?"], "keywords": ["text-to-image", "hero image", "image generation", "AI", "Leonardo"], "id": 60}, "MCP_Redis-MCP_hset": {"original": "Set a field in a hash stored at key with an optional expiration time.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n    value: The value to set.\n    expire_seconds: Optional; time in seconds after which the key should expire.\n\nReturns:\n    A success message or an error message.\n", "paraphrases": ["Store a specific value under a designated field within a Redis hash, with the option to set an expiration time for the hash itself.", "Update or insert a key-value pair into a Redis hash, identified by its main key, and optionally configure a lifespan for that entire hash.", "Assign a value to a particular field within a Redis hash specified by its top-level key, optionally enabling an automatic expiry for the hash.", "Execute an operation to set a field's value inside a Redis hash, allowing for an optional time-to-live (TTL) to be applied to the hash key.", "Persist a value to a field within a Redis hash, where the hash itself can be configured to expire after a specified number of seconds."], "asking": ["I need a node that allows me to set a specific field within a Redis hash with a new value. It should also have an optional parameter to make the entire hash expire after a certain number of seconds.", "Could you build a function that updates or creates a field inside a Redis hash? It's important that I can also optionally set a time limit after which the whole hash key will be deleted.", "I'm looking for a way to store data in a particular field of a Redis hash. On top of that, I'd like the option to automatically remove the entire hash after a specified duration.", "We need a utility to modify a field's value within an existing Redis hash, or add it if it doesn't exist. Crucially, this utility should also support setting an expiration on the parent hash key itself.", "Please provide a node that facilitates writing a value to a named field in a Redis hash, and includes the capability to apply an optional Time-To-Live (TTL) to the hash containing that field."], "keywords": ["Redis", "HSET", "Hash", "Set", "Field", "Expiration"], "id": 61}, "MCP_Redis-MCP_hget": {"original": "Get the value of a field in a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n    key: The field name inside the hash.\n\nReturns:\n    The field value or an error message.\n", "paraphrases": ["Retrieve the value of a specific field from a Redis hash, using the hash's key and the field's name.", "Obtain the data associated with a particular field within a Redis hash, which will return either the field's value or an error.", "Access the content of a field inside a Redis hash by providing both the hash key and the field identifier.", "Extract a field's value from a Redis hash, given the hash's unique key and the field's name.", "Fetch the value of a designated field from a Redis hash, where the hash name and field name are supplied as arguments."], "asking": ["I need a way to pull out a specific value from a Redis hash, given both the hash's name and the particular field I'm interested in.", "How can I access the value of an attribute that's stored within a Redis hash? I know the hash's key and the attribute's key.", "Could you create a function that allows me to retrieve data for a specific key within a Redis hash? I'd pass in the name of the hash and the field's key.", "My application stores structured data in Redis hashes, and sometimes I just need to read a single property from one of them. I'll have the hash's main key and the property's key available.", "I require a utility to perform a lookup for a specific field's value within a designated Redis hash. The input would be the hash's primary key and the target field's key."], "keywords": ["Redis", "hget", "Get", "Hash", "Field", "Value", "MCP"], "id": 62}, "MCP_Redis-MCP_hgetall": {"original": "Get all fields and values from a Redis hash.\n\nArgs:\n    name: The Redis hash key.\n\nReturns:\n    A dictionary of field-value pairs or an error message.\n", "paraphrases": ["Retrieve all field-value pairs stored in a Redis hash, identified by its key, returning them as a dictionary or an error message.", "Fetch the complete set of fields and their associated values from a specified Redis hash, using its key. The outcome is a dictionary or an error.", "Extract every field and its value from a given Redis hash, referenced by its key, and present them in a dictionary format or as an error.", "Access the full content—all fields and their corresponding values—of a Redis hash by providing its key. A dictionary or an error will be returned.", "Obtain all the data (fields and values) from a Redis hash by providing its unique key, resulting in a dictionary or an error response."], "asking": ["I need a node that can retrieve all the field-value pairs from a specific Redis hash. I'll provide the hash key, and it should return a dictionary containing all its data.", "Can you implement a function to fetch the complete contents of a Redis hash? Given the hash's name, it should output a dictionary mapping all the fields to their respective values.", "I'm looking for a way to extract every single field and its corresponding value from a Redis hash. If I give it the hash identifier, I want a full dictionary of its data.", "We need to read an entire Redis hash. The node should take the hash key as input and return all the associated fields and values in a dictionary format.", "Create a node that, when given a Redis hash key, will provide a comprehensive dictionary of all the fields and their current values within that specific hash."], "keywords": ["MCP", "Redis", "hgetall", "hash", "get"], "id": 63}, "MCP_CMS-MCP_create_webflow_blog_post": {"original": "Create a blog post in Webflow. This tool automatically analyzes the blog post content, creates the necessary collection fields if they don't exist, and posts the blog content. No need to specify collection fields - they are generated automatically based on the blog post data.", "paraphrases": ["To publish a blog post in Webflow, simply provide your content. The tool will automatically analyze it, generate any necessary collection fields if they're missing, and then post the content. There's no need for manual collection field definition, as they are created automatically from your blog post's data.", "Creating a blog post in Webflow is automated: the system analyzes the content, dynamically establishes required collection fields if they don't exist, and publishes the post. Users do not need to specify collection fields, as they are generated based on the post's information.", "When you compose a blog post in Webflow, the platform takes care of everything. It autonomously analyzes your content, sets up the appropriate collection fields where needed, and then publishes the post. Manual configuration of collection fields is unnecessary, as they are automatically derived from the blog post data.", "Webflow simplifies blog post creation by automatically analyzing the content, creating any essential collection fields if not present, and then publishing the post. You won't need to manually define collection fields, as they're generated automatically from your blog post's data.", "With this Webflow feature, users can create blog posts, and the system will automatically process the content. It intelligently generates collection fields as required, without manual input, and then publishes the blog post, saving you the task of pre-defining those fields."], "asking": ["I need a tool that can automatically take my blog post content and publish it directly to Webflow. The crucial part is that it should be smart enough to analyze the data, create any necessary Webflow collection fields if they don't already exist, and then post the content, all without me having to manually specify or pre-configure those fields.", "Can you create a node that allows me to publish new blog posts on my Webflow site? It needs to be highly automated, meaning it should intelligently determine what collection fields are required based on the incoming content, create them if they're missing, and then publish the post. I want to avoid any manual Webflow collection field setup.", "I'm looking for a Webflow integration that handles blog post creation. My main requirement is that it should automatically adapt to the blog post content I provide, generating any new collection fields in Webflow as needed, and then posting the article. I don't want to have to define the collection fields beforehand; it should just figure it out.", "We need a node to push blog content to Webflow. The key feature is that it must analyze the blog post data, dynamically create any missing collection fields in Webflow, and then publish the post. The goal is a seamless process where I don't have to specify or manage the Webflow collection fields manually at all.", "How can I get a node that automates blog post publishing to Webflow? It needs to be self-sufficient: take the content, understand its structure, automatically create any required Webflow collection fields if they're not there, and then post the blog. I definitely want to skip the step of pre-defining Webflow collection fields."], "keywords": ["Webflow", "Blog Post", "Create", "Automated", "CMS", "Collection Fields", "Schema Generation"], "id": 64}, "MCP_Redis-MCP_set": {"original": "Set a Redis string value with an optional expiration time.\n\nArgs:\n    key (str): The key to set.\n    value (str): The value to store.\n    expiration (int, optional): Expiration time in seconds.\n\nReturns:\n    str: Confirmation message or an error message.\n", "paraphrases": ["Stores a string value in Redis identified by a given key, with the option to set an expiration time in seconds. It requires a key and value, plus an optional expiration, and outputs a status message.", "This operation writes a string to a Redis key, and you can specify an optional duration in seconds after which it should automatically be removed. It takes the key, value, expiration (optional), and returns a string confirmation.", "To save a string in Redis, use this method with a key and the string itself. You have the choice to make it expire after a certain number of seconds. The output will be a success or error message.", "Persist a string `value` in Redis under a specific `key`, with the flexibility to include an optional `expiration` time in seconds. The function returns a descriptive string regarding the operation's result.", "This command sets a Redis string value for a given key, allowing for an optional time-to-live (TTL) parameter in seconds. It provides a string indicating the outcome of the set operation."], "asking": ["Could you provide a way to store a string value in Redis, and also allow me to specify how long it should persist before being automatically removed?", "I need a node that can execute a Redis `SET` command. It should accept a key and a string value, and ideally, an optional argument for setting a time-to-live.", "I'm looking for functionality to insert data into Redis as a simple string, and it's crucial that I can set an expiry duration for this data if needed. What's available for that?", "Is there a tool or function that lets me put a text value into a Redis database, and importantly, lets me decide if it should expire after a certain number of seconds?", "I require a mechanism to write a string to Red<PERSON>, where I can define a specific lifetime for that entry. Essentially, a `SET` operation with an optional `EXPIRE` parameter."], "keywords": ["Redis", "set", "string", "key", "value", "expiration"], "id": 65}, "MCP_Redis-MCP_get": {"original": "Get a Redis string value.\n\nArgs:\n    key (str): The key to retrieve.\n\nReturns:\n    str: The stored value or an error message.\n", "paraphrases": ["Retrieves a string value from Redis using a specified key, returning the value or an error message.", "Obtain a string value from Redis by providing a key; the function returns either the value found or an error message.", "This operation fetches a string value from Redis. It requires a string key and yields the stored value or an error string.", "Fetches the string value associated with a given key from Redis, providing either the value or an error message as the result.", "Reads a string value from Redis; supply a string key to get the corresponding value, or an error message if the operation fails."], "asking": ["I need a node that can fetch a string value from Red<PERSON>, given a key.", "How can I retrieve data stored as a string in my Redis database? Is there a function for that?", "Could you create a node that allows me to get a specific string from Redis by providing its key?", "I have a Red<PERSON> key and I need to get the string content associated with it. What's the best way to achieve this, or is there a suitable node?", "I'm looking for a capability to query a Redis instance to extract a string value, where I specify the key and it returns the associated string."], "keywords": ["MCP", "Redis", "Get", "Retrieve", "String", "Value", "Key"], "id": 66}, "MCP_Google_Sheets_get_values_in_range": {"original": "Get all values or values from a range of cells using A1 notation", "paraphrases": ["Retrieve data from individual cells or an entire cell range, specified by A1 notation.", "Access all cell contents or a specific range of cells using A1 referencing.", "Obtain values from single cells or a continuous block of cells, employing A1 style.", "Extract cell data, whether from all cells or a designated range, via A1 notation.", "Utilize A1 notation to fetch values from either single cells or a range of cells."], "asking": ["Could you create a node that allows me to retrieve the contents of a cell or a specified range of cells, perhaps by using the standard A1 cell referencing system?", "I need a way to extract data from a spreadsheet, where I can point to an individual cell like 'A1' or a whole section like 'B2:D5' and get its values. Is there a node for that?", "I'm looking for a node to read cell values from my sheets. It should let me specify the cells using A1 notation, whether it's just one cell or an entire range.", "How can I fetch the data stored in a particular cell or a block of cells from a sheet? I'd like to use the common A1 format for defining the cells.", "My workflow requires pulling out specific data points or tables from a spreadsheet. Can we implement a node that takes an A1 cell reference or range and returns the corresponding cell data?"], "keywords": ["Google Sheets", "get values", "read", "range", "A1 notation", "cells", "spreadsheet", "data"], "id": 67}, "MCP_Google_Forms_get_google_form_responses": {"original": "Get responses for a Google Form", "paraphrases": ["Collect submissions from a Google Form.", "Obtain replies to your Google Form.", "Receive answers to a Google Form.", "View the data submitted through a Google Form.", "Access the results of a Google Form."], "asking": ["I need to extract all the submissions from my Google Form.", "How do I retrieve the data that's been entered into a Google Form?", "Can we get the collected responses from a Google Form?", "I want to pull the answers directly from my Google Form.", "Provide a method to access the entries submitted via a Google Form."], "keywords": ["Google Forms", "get", "responses", "form responses"], "id": 68}, "MCP_PDF_Reader_extract_metadata": {"original": "Extract metadata information from a file by URL including source, file_url, file_name, format, size, and other file properties", "paraphrases": ["Retrieve metadata from a file using its URL, including details like source, file_url, file_name, format, size, and other characteristics.", "Obtain descriptive information about a file from its web address, such as its origin, direct link, name, type, dimensions, and additional attributes.", "Gather file properties via a URL, specifically covering its source, web location, identifier, format, byte size, and other associated details.", "Access and extract various file details for a file specified by URL, encompassing its source, the URL itself, the file's name, its structure, magnitude, and any other pertinent data.", "Isolate and collect metadata points for a file accessible by URL, like its source, specific link, assigned name, file type, data size, and other intrinsic features."], "asking": ["I need a way to automatically grab all the relevant details about a file just by giving it its web address. Things like its name, type, size, and where it came from.", "Can you create a component that takes a file URL and gives me back a comprehensive set of properties for that file, like its format, size, and original source?", "I'm looking for a function that can inspect a file located at a given URL and extract all available metadata, including the file's name, type, size, and its direct link.", "We need a node that, when provided with a URL, will fetch and return structured information about the hosted file, such as its name, format, and byte size, along with the source URL itself.", "Is there a tool or a node that can take a URL to a file and provide a summary of its key attributes, specifically its name, file type, and how big it is?"], "keywords": ["metadata extraction", "PDF Reader", "file", "URL", "document properties"], "id": 69}, "MCP_PDF_Reader_extract_file_content": {"original": "Extract content from PDF, DOC, DOCX, CSV, or XLSX files with optional pagination and search for spreadsheets", "paraphrases": ["Retrieve information from PDF, Word (DOC, DOCX), and Excel (CSV, XLSX) documents, featuring optional pagination and the ability to search specifically within spreadsheets.", "Access data from PDF, DOC, DOCX, CSV, and XLSX files, with flexible options for paginating results and conducting searches within spreadsheet content.", "This system enables content extraction from various file formats including PDF, Word, and Excel, offering both optional pagination and a search function for spreadsheets.", "Pull data from PDF, DOC, DOCX, CSV, and XLSX files, complete with customizable pagination and a dedicated search feature for spreadsheet-based information.", "The tool allows you to extract content from PDF, Word, and spreadsheet files, providing choices for pagination and the capability to search for data within spreadsheets."], "asking": ["I need a way to reliably extract information from various document types, including PDFs, Word documents (both old and new formats), and spreadsheet files like CSVs and Excels. It's crucial that I can specify page ranges for longer documents and also search for and pull specific data from any spreadsheet content.", "Could we get a component that handles diverse document processing? It should be capable of extracting content from PDFs, DOCs, DOCXs, CSVs, and XLSX files. For multi-page documents, pagination support is a must, and it needs to be able to identify and extract data from embedded or standalone spreadsheets.", "I'm looking for a node that can parse and retrieve data from a mix of file formats: PDF, DOC, DOCX, CSV, and XLSX. This tool should provide options for paginated extraction when dealing with lengthy documents, and importantly, allow for searching and targeted extraction of data within spreadsheet-like structures.", "Implement a robust document content extractor. It must support PDF, DOC, DOCX, CSV, and XLSX file inputs. Key features should include pagination controls for multi-page documents and the ability to search for and extract specific data from all detected spreadsheet content.", "My workflow requires a flexible content extraction utility. It needs to pull text and data from a variety of sources including PDFs, Word documents, and common spreadsheet formats like CSV and XLSX. Ideally, it would offer pagination for large files and provide capabilities to specifically search for and retrieve information from spreadsheets."], "keywords": ["extraction", "content", "file", "document", "PDF", "DOC", "DOCX", "spreadsheet", "CSV", "XLSX", "pagination", "search"], "id": 70}, "MCP_Redis_MCP_delete": {"original": "Delete a Redis key.\n\nArgs:\n    key (str): The key to delete.\n\nReturns:\n    str: Confirmation message or an error message.\n", "paraphrases": ["This operation removes a specified key from the Redis database. It requires a string `key` as input and returns a message indicating success or an error.", "To delete a Redis entry, provide the `key` (string) as an argument. The function will return a string confirming the deletion or reporting any issues.", "The purpose is to erase a key from Redis. It takes a string `key` that identifies the target and provides a string response detailing the outcome.", "You can remove an item from Redis by supplying its `key` as a string argument. The result is a string message, either a confirmation or an error.", "This function permanently discards a Redis key. It needs the `key` as a string parameter and outputs a string indicating whether the deletion was successful."], "asking": ["I need a way to completely remove a specific key from our Redis database. I'll provide the exact key name to be deleted.", "My application requires a mechanism to clean up outdated or irrelevant data from Redis. Essentially, I need to delete entries by their unique key identifier.", "Develop a utility that accepts a string representing a Redis key and executes a deletion command for that key within the Redis instance.", "I'm looking for a function or action that can just take a Redis key and make it disappear from the store permanently.", "Could you implement the Redis 'DEL' command functionality, allowing me to specify which particular key should be removed?"], "keywords": ["Redis", "delete", "key", "MCP"], "id": 71}, "SplitTextComponent": {"original": "Splits text into a list using a delimiter.", "paraphrases": ["Divides a string into a collection of items, determined by a specific separator.", "Separates a piece of text into an ordered list of elements, using a designated character.", "Breaks down a body of text into an array, employing a particular divider.", "Converts a single string into multiple substrings, with breaks defined by a marker.", "Parses a block of text into individual components, identified by a chosen delimiter."], "asking": ["I need a node that can take a single string and break it down into multiple pieces, putting each piece into a list. It should use a character or sequence I specify to decide where to make the splits.", "Can you create a utility that accepts a block of text and then divides it into an array of smaller strings, with the division points being determined by a custom separator?", "I'm looking for a way to parse a continuous string into a list of its constituent parts, using a specific delimiter as the criterion for separation.", "How can I get a node that takes a text input and generates a list, where each element in the list is a segment of the original text, split at every occurrence of a user-defined delimiter?", "Please provide a node that tokenizes a given string into a list of substrings, using a provided delimiter as the splitting criterion."], "keywords": ["split", "text", "delimiter", "list", "string", "separate", "text processing"], "id": 72}, "workflow-e840128c-5017-486a-9374-da5428dc0e6d": {"original": "CREATE_Document_--_Workflow_(Blog_Gen)", "paraphrases": ["Blog content generation workflow.", "Process for creating blog documents.", "Structured approach to generating blog posts.", "Workflow for authoring blog entries.", "Systematic creation of blog articles."], "asking": ["I need a node that handles the creation of a document specifically within our blog generation workflow.", "Can you set up a step for creating a document as part of the overall blog content production process?", "We require a node to generate a document, integrated into the blog post workflow.", "Please create a node responsible for document creation in the context of our blog generation system.", "I'm looking for a component that will create a document, designed for the blog generation pipeline."], "keywords": ["workflow", "create", "document", "blog generation", "ID"], "id": 73}, "MCP_Google_Sheets_count_column_values": {"original": "Count the total number of values in a specific column", "paraphrases": ["Determine the quantity of entries within a particular column.", "Ascertain the number of items in a given column.", "Calculate the total count of values for a specified column.", "Find out how many data points are present in a chosen column.", "Tally the elements contained within one specific column."], "asking": ["Could you provide a node that calculates the total number of entries found within a specified column?", "I need a way to count all the values present in a particular data field. What's the node for that?", "Generate a function that can give me a grand total of all records within any given column of my dataset.", "To get a summary, I'm looking for a node that simply counts how many items are in a selected column.", "How can I determine the full count of values in a specific column? I require a node that performs this aggregation."], "keywords": ["Google Sheets", "count", "column", "values", "counting", "spreadsheet"], "id": 74}, "MCP_Google_Sheets_set_formula": {"original": "Set a formula in a specific cell of a Google Sheet", "paraphrases": ["Apply a formula to a particular cell within a Google Sheet.", "Insert a formula into a designated cell in a Google Sheet.", "Enter a formula into a precise cell of a Google Sheet document.", "Assign a formula to a chosen cell in a Google Spreadsheet.", "Place a formula within an identified cell of a Google Sheet."], "asking": ["I need a node that allows me to input a formula and then apply it to a specific cell in a Google Sheet.", "Can you build a feature where I can define a calculation and have it automatically placed into a designated cell of a Google Spreadsheet?", "I'm looking for a way to programmatically set a formula for a particular cell within a Google Sheet. How can I request this functionality?", "My workflow requires injecting a specific formula into a single, targeted cell on a Google Sheet. Is there a node available or planned for this?", "We need a component that can take a formula string and write it directly into a user-specified cell in a Google Sheets document."], "keywords": ["Google Sheets", "formula", "set", "sheet", "cell"], "id": 75}, "MCP_Jira_&_Confluence_get_issue": {"original": "Get a Jira issue by key.", "paraphrases": ["Retrieve a Jira issue using its key.", "Find a specific Jira issue based on its key.", "Obtain a Jira issue by providing its key.", "Locate a Jira issue via its unique key.", "Access the Jira issue corresponding to the provided key."], "asking": ["I need to retrieve a specific Jira issue using its unique key. How can I implement that?", "How do I fetch the details of a particular Jira ticket if I already know its identifier?", "Could you create a node that allows me to look up a Jira issue based on its ID?", "I want to get all the information for a single Jira item, given its issue key.", "What's the process for pulling up a specific Jira issue by providing its key?"], "keywords": ["<PERSON><PERSON>", "Confluence", "Issue", "Get", "Key", "MCP"], "id": 76}, "MCP_Google_Sheets_update_row": {"original": "Update a row in a spreadsheet", "paraphrases": ["Modify a spreadsheet entry.", "Edit a record in a digital table.", "Change the data in a specific worksheet row.", "Alter an existing line within a spreadsheet.", "Revise information for a row in a digital sheet."], "asking": ["I need to modify the data in an existing row within one of my spreadsheets.", "Is there a way to edit a specific entry in my Google Sheet or Excel file?", "I'm looking for a tool that lets me change the values in a particular row of a spreadsheet table.", "Can you provide a node or function that enables the updating of a designated row's fields in a given spreadsheet?", "My goal is to revise certain details in a row that already exists in my spreadsheet."], "keywords": ["Google Sheets", "update row", "spreadsheet", "update"], "id": 77}, "MCP_Google_Document_get_document": {"original": "Retrieve the content of a Google Document by its ID", "paraphrases": ["Fetch the data stored within a Google Document, using its unique identifier.", "Access a Google Document's content by providing its ID.", "Extract the information from a Google Doc when given its specific ID.", "Obtain the material contained in a Google Drive Document via its ID.", "Get the text of a Google Document, referencing its identifier."], "asking": ["I need a way to grab the actual text or content from a Google Document, given its specific ID.", "Could you provide a component that, when given a Google Document ID, returns the full body of the document's text?", "I'm looking for a node that can pull the entire written content from a Google Doc if I provide its unique identifier.", "My workflow requires me to fetch the content of various Google Docs based on their IDs. Is there a node for that?", "How do I extract the content of a particular Google Document using just its ID?"], "keywords": ["Google", "Document", "Retrieve", "Content", "Get"], "id": 78}, "MCP_Gmail_create_draft": {"original": "Create a draft email message", "paraphrases": ["Draft an email.", "Compose an email draft.", "Generate a preliminary email.", "Write an initial email.", "Prepare an email in draft form."], "asking": ["I need a node that can generate a draft email message. Can you implement that functionality?", "Could you create a module that is specifically designed to compose preliminary email drafts?", "My current task requires a step to produce a draft email. How can I add a node for 'Create a draft email message'?", "Please provide the capability to initiate the drafting of an email message. I'm looking for a node to handle this.", "I want to set up a component that outputs a fully formed draft email. Can we get a node for generating email drafts?"], "keywords": ["MCP", "Gmail", "create", "draft", "email", "message"], "id": 79}, "MCP_SDR_Management_get_campaign": {"original": "Get campaign details by ID", "paraphrases": ["Retrieve campaign information by its identifier.", "Fetch campaign specifics using the provided ID.", "Obtain the details of a campaign based on its unique ID.", "Access campaign data by specifying the ID.", "Request campaign-related information via its given ID."], "asking": ["I need a way to fetch all the specific information related to a campaign, provided I have its unique identifier.", "Could you implement a node that allows me to retrieve the complete details for any given campaign, using its ID as the primary input?", "I have a campaign ID and require a function to access and display all the associated data for that particular campaign.", "Generate a tool that, when given a campaign's ID, returns the comprehensive set of details pertaining to that specific campaign.", "I'm looking for a method to look up and obtain all the relevant information for a single campaign based solely on its designated ID."], "keywords": ["MCP", "SDR", "management", "get", "campaign", "details", "ID"], "id": 80}, "MCP_SDR_Management_create_customers": {"original": "Bulk create customers for a campaign from a list of Apollo person objects", "paraphrases": ["Generate multiple customer accounts for a specific campaign, utilizing data sourced from Apollo person objects.", "Import numerous customer profiles into a campaign directly from a provided list of Apollo person objects.", "Provision a large quantity of campaign customers by leveraging a list composed of Apollo person objects.", "Mass-create campaign-associated customers using an existing inventory of Apollo person objects.", "Transform a collection of Apollo person objects into many new customer records for a particular campaign."], "asking": ["I need to quickly get all the contacts from my Apollo list into our customer database, specifically for the new campaign we're launching. Can we get a node that handles this bulk import?", "How can I take a large set of Apollo person objects and turn them into customer records for a specific campaign, all at once?", "We're looking for a node that can mass-create customer entries for a campaign, pulling the necessary data directly from a provided list of Apollo profiles.", "Please provide a node that automates the bulk creation of customer accounts for a designated campaign, using a list of Apollo.io person objects as the source.", "I have a long list of prospects from Apollo.io, and I need an efficient way to add them all as new customers and link them to our current campaign. Is there a node for that functionality?"], "keywords": ["customer creation", "bulk operations", "SDR", "campaigns", "Apollo"], "id": 81}, "MCP_Apollo_IO_people_enrichment": {"original": "Use the People Enrichment endpoint to enrich data for 1 person", "paraphrases": ["Enrich data for a single individual by calling the People Enrichment endpoint.", "To enhance one person's information, make use of the People Enrichment endpoint.", "Access the People Enrichment endpoint to augment the data of one specific person.", "Leverage the People Enrichment endpoint to add more detail to a single person's profile.", "Employ the People Enrichment service to enrich the data belonging to one individual."], "asking": ["I need to get more detailed information about a single individual. Can you add a step that uses our person enrichment service for just one record?", "Let's integrate a process to enrich the data for one specific person. Please include the node that handles individual people enrichment.", "My goal is to enhance a single person's profile with additional data. Could you set up the call to the People Enrichment endpoint for a lone individual?", "Could you implement a step that utilizes the people data enrichment tool for one person at a time?", "I want to enrich a solitary person's information. Please add the node for enriching data for a single individual using the relevant service."], "keywords": ["Apollo.io", "People Enrichment", "Data Enrichment", "Person", "API", "MCP"], "id": 82}, "MCP_Apollo_IO_people_search": {"original": "Use the People Search endpoint to find people", "paraphrases": ["To locate individuals, utilize the People Search endpoint.", "The People Search endpoint enables you to find people.", "You can discover people by accessing the People Search endpoint.", "Find individuals by making use of the People Search endpoint.", "Employ the People Search endpoint to search for people."], "asking": ["I need a node that allows me to search for specific people. The primary goal is to find individuals based on various criteria.", "Can we get a node that exposes the functionality of the People Search endpoint? I want to be able to use it to locate people.", "I'm looking for a way to implement a 'find user' feature. This node should handle searching for people.", "We require a component that can query the system's database to retrieve person records. Essentially, a people lookup tool.", "Could you build a node that specifically utilizes the People Search API to fetch details about individuals?"], "keywords": ["Apollo_IO", "People Search", "Search", "People", "Endpoint"], "id": 83}, "MCP_SDR_Management_list_products": {"original": "List products for a user (optionally filter by campaign_id)", "paraphrases": ["Display products to a user, with an optional campaign ID filter.", "Present a list of products for a user, which can be optionally narrowed by campaign_id.", "Retrieve and show products to a user, with the ability to optionally filter by a campaign identifier.", "Generate a product listing for a user, allowing for an optional campaign ID to refine the output.", "Provide a user with a product display, optionally filtered by a specific campaign's ID."], "asking": ["I need a way to show users a list of products. It would be very useful if we could also filter that list by a specific campaign ID, but that shouldn't always be mandatory.", "Can we build a feature where a user can see all available products? There's an additional requirement to sometimes narrow down this product display to items related to a particular marketing campaign.", "Let's create a capability to retrieve a list of products for a user. It needs to have an optional parameter for a `campaign_id` so we can selectively display products tied to certain campaigns.", "How can we present our product offerings to customers? And critically, we need the flexibility to sometimes only show products that are part of a specific campaign.", "I'm requesting a node that lists products for a user. The key is that it should support an optional filter for `campaign_id` to refine the product selection as needed."], "keywords": ["products", "list", "SDR", "management", "campaign", "filter", "campaign_id", "MCP"], "id": 84}, "MCP_SDR_Management_create_email_conversation": {"original": "Create a new email conversation between customer and user", "paraphrases": ["Start a new email exchange between a customer and a user.", "Compose a fresh email dialogue involving a customer and a user.", "Generate an initial email conversation between a customer and a user.", "Initiate a new email thread with a customer and a user.", "Begin a new email communication between a customer and a user."], "asking": ["I need a node that can generate a new email conversation, specifically an initial exchange between a customer and a user, like a support agent.", "Could you create a function to simulate an email conversation from scratch, involving a customer's opening message and a user's first reply?", "I'm looking for a way to quickly generate an example email conversation between a customer and a general user, starting a new thread.", "Generate a node that takes a scenario and produces a fresh email conversation, showing both the customer's initial email and the user's response.", "Is there a component that can initiate a new email conversation flow, providing the text for a customer's query and a user's subsequent reply?"], "keywords": ["create", "email", "conversation", "customer", "user", "MCP", "SDR", "management", "communication"], "id": 85}, "MCP_SDR_Management_get_email_conversations": {"original": "Get email conversations with optional filtering", "paraphrases": ["Retrieve email threads, with the option to filter.", "Access email discussions, with filtering available as an option.", "Obtain email exchanges, allowing for optional filtering.", "Receive email communications, which can be optionally filtered.", "View email dialogues, with the choice to apply filters."], "asking": ["I need a node that can retrieve email conversations, and it's essential that I have the option to apply filters to narrow down the results.", "Could you create a component that fetches email threads, allowing me to specify criteria like sender, date, or keywords to filter which conversations are returned?", "I'm looking for a way to extract email conversations, and I require the flexibility to filter them based on various attributes before I get the output.", "Implement a function that retrieves email conversations, with the critical feature of optional filtering parameters to refine the selection.", "Please provide a node that gets email conversations, and importantly, includes an optional filtering capability so I can get exactly what I need."], "keywords": ["MCP", "SDR", "Management", "email", "conversations", "get", "filtering"], "id": 86}, "MCP_SDR_Management_reply_email_from_customer": {"original": "Create an email conversation record from a customer's reply using from/to emails (resolves user_id, customer_id, latest campaign)", "paraphrases": ["Generate an email conversation log from a customer's response, utilizing the sender and recipient emails to resolve the user_id, customer_id, and latest campaign.", "Document an email exchange thread, originating from a customer's reply, by using the 'from' and 'to' addresses to ascertain the associated user, customer, and active campaign.", "Establish a record of an email conversation, initiated by a customer's response, where the email addresses are used to identify the relevant user_id, customer_id, and latest campaign.", "Formulate a customer email interaction history from an incoming reply, leveraging the 'from' and 'to' fields to link it to a user, customer, and their most recent campaign.", "Compile an email conversation entry based on a customer's reply, employing the 'from' and 'to' emails to deduce the corresponding user_id, customer_id, and latest campaign."], "asking": ["I need a component that can take an incoming customer email reply and, using the sender and recipient addresses, figure out who the user is, which customer account they're part of, and which campaign they're actually responding to.", "We need a way to automatically link customer replies to their correct context. So, when an email reply comes in, I want it to tell me the specific user who replied, the customer they represent, and the last campaign they interacted with, all based on the email headers.", "Could we get a system that processes email replies? The main goal is to extract the sender and receiver emails, and then use that to determine the user ID, the customer ID, and the most recent campaign that was sent to them.", "I'm looking for a node that can attribute customer replies. Given an email reply, it should use the 'from' and 'to' fields to pinpoint the exact user, their associated customer, and the latest campaign that likely triggered their response.", "Please create a function or module that, upon receiving a customer's email reply, can parse the 'from' and 'to' email addresses to resolve and return the corresponding user_id, customer_id, and the latest campaign associated with that interaction."], "keywords": ["Email Conversation", "Customer Reply", "Record Creation", "SDR", "User ID", "Customer ID", "Campaign"], "id": 87}, "MCP_SDR_Management_fetch_customer": {"original": "Fetch customer details by customer_id and user_id", "paraphrases": ["Retrieve customer information using the customer_id and user_id.", "Get customer data based on the provided customer_id and user_id.", "Access client records by their customer_id and user_id.", "Obtain customer details for a specific user using customer_id and user_id.", "Query for customer profiles, requiring both customer_id and user_id."], "asking": ["I need to get all the information we have for a customer, provided I have both their customer ID and the associated user ID.", "Could you create a function or endpoint that retrieves a customer's full profile using their unique customer_id and the corresponding user_id?", "We require a way to access a customer's detailed record. The lookup should be performed using both the customer_id and the user_id as lookup keys.", "How can I find out all the specifics about a particular customer? I'll have their customer ID and the user ID linked to them.", "I need a request handler that can return comprehensive customer data. It must take both a customer_id and a user_id as input parameters."], "keywords": ["MCP", "SDR", "Management", "<PERSON>tch", "Get", "Retrieve", "Customer", "Details", "Customer ID", "User ID"], "id": 88}}