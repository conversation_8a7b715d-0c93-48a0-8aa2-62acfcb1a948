import json

import numpy as np
import argparse
from sentence_transformers import SentenceTransformer

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str, required=True)
    args = parser.parse_args()
    model = args.model
    model = SentenceTransformer(model, trust_remote_code=True)
    folder_name = model.split("/")[-1]
    name_embeddings = np.load(f"embeddings/{folder_name}/names_embeddings.npy")
    description_embeddings = np.load(f"embeddings/{folder_name}/descriptions_embeddings.npy")
    info_embeddings = np.load(f"embeddings/{folder_name}/info_embeddings.npy")
    l = len(name_embeddings)
    print("Loaded embeddings")

    paraphrases = json.load(open("paraphase_output.json", "r"))
    output = {}
    for k, v in paraphrases.items():
        output[k] = []
        for p in v["paraphrases"]:
            temp = {}
            temp["type"] = "paraphrase"
            temp["query"] = p
            p_embedding = model.encode(p)
            p_embedding = np.array(p_embedding[0])
            temp["score"] = []
            for i in range(l):
                loop_temp = {}
                score = np.dot(p_embedding, name_embeddings[i])
                loop_temp["comparision_id"] = i
                loop_temp["score"] = score
                temp["score"].append()
            output[k].append(temp)
    with open(f"similarity_score/{folder_name}.json", "w") as f:
        json.dump(output, f, indent=2)
