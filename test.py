from sentence_transformers import SentenceTransformer

model = SentenceTransformer("google-bert/bert-base-uncased", trust_remote_code=True)
model.max_seq_length = 256
sentences = ['search_document: TSNE is a dimensionality reduction algorithm created by <PERSON><PERSON>',
             'search_document: TSNE is a dimensionality reduction algorithm created by <PERSON><PERSON>']
embeddings = model.encode(sentences)
print(embeddings)
print(len(embeddings))
print(len(embeddings[0]))