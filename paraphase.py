import json
import os
import time

from openai import OpenAI

REQUESTY_API_KEY = "sk-PcLyYtnhSm2UntRhL6aECcw1ZGxqbDoeU8midASAgsSwAuh/Sy5cX0YF6/O/tkyvDFvdKJLrmJMFY4z7ha7obgkcsoPHiwQnO4icZabGGg4="
client = OpenAI(
    api_key=REQUESTY_API_KEY,
    base_url="https://router.requesty.ai/v1",
)


def paraphase(name, description):
    output = {}
    output["original"] = description

    def clean_response(response):
        # Extract JSON only
        if "```json" in response:
            response = response.split("```json")[1].split("```")[0].strip()
        elif "```" in response:  # fallback in case it's just ``` without json
            response = response.split("```")[1].split("```")[0].strip()
        return response.strip()

    # --- 1. Paraphrases ---
    prompt = f"""
Return ONLY a JSON list of 5 paraphrases of the following text. 
Do not include comments, explanations, or markdown fences. 

Text:
{description}
"""
    response = client.chat.completions.create(
        model="google/gemini-2.5-flash", 
        messages=[{"role": "user", "content": prompt}]
    )
    response = clean_response(response.choices[0].message.content)
    output["paraphrases"] = json.loads(response)

    # --- 2. Asking-style paraphrases ---
    prompt = f"""
Take the following node description:

{description}

Generate 5 paraphrased requests as if a person or an LLM were asking for this node. 
Each paraphrase should sound like someone is describing what they need or are requesting this node to be created. 

Return ONLY a JSON list. No markdown, no explanations.
"""
    response = client.chat.completions.create(
        model="google/gemini-2.5-flash", 
        messages=[{"role": "user", "content": prompt}]
    )
    response = clean_response(response.choices[0].message.content)
    output["asking"] = json.loads(response)

    # --- 3. Keywords ---
    prompt = f"""
You are given a node with the following details:

Node Name: "{name}"
Node Description: "{description}"

Extract the most relevant keywords that define the purpose and identity of the node. 
Think in terms of domain, function, and unique identifiers. 

Return ONLY a JSON list of keywords. No markdown, no explanations.
"""
    response = client.chat.completions.create(
        model="google/gemini-2.5-flash", 
        messages=[{"role": "user", "content": prompt}]
    )
    response = clean_response(response.choices[0].message.content)
    output["keywords"] = json.loads(response)

    return output

def main():
    path = "./node_context.json"
    with open(path, "r") as f:
        data = json.load(f)
    final_output = {}
    for node in data:
        for i in range(3):
            try:
                node = data[node]
                name = node["type"]
                description = node["description"]
                output = paraphase(name, description)
                final_output[name] = output
                time.sleep(5)
                break
            except Exception as e:
                print(f"Error with {name}")
                print(e)
        print(f"Done with {name}")
    with open("paraphase_output.json", "w") as f:
        json.dump(final_output, f, indent=2)

if __name__ == "__main__":
    main()