import argparse
import json
import numpy as np
from sentence_transformers import SentenceTransformer
import os
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str, required=True)

    args = parser.parse_args()
    model = args.model
    folder_name = model.split("/")[-1]

    data = json.load(open("node_context.json", "r"))
    id_to_node = json.load(open("id_to_node.json", "r"))
    print("Loaded data")
    names = []
    descriptions = []
    info = []
    l = len(id_to_node)
    for i in range(l):
        node = data[id_to_node[str(i)]]
        names.append(node["name"].strip())
        descriptions.append(node["description"].strip())
        info.append(node["node_info"] + node["input_info"] + node["output_info"].strip())
    print("Loaded names, descriptions, info")
    model = SentenceTransformer(model, trust_remote_code=True)
    model.max_seq_length = 256
    names_embeddings = model.encode(names)
    descriptions_embeddings = model.encode(descriptions)
    info_embeddings = model.encode(info)
    names_embeddings = np.array(names_embeddings)
    descriptions_embeddings = np.array(descriptions_embeddings)
    info_embeddings = np.array(info_embeddings)
    print("Encoded")
    os.makedirs(f"embeddings/{folder_name}", exist_ok=True)
    np.save(f"embeddings/{folder_name}/names_embeddings.npy", names_embeddings)
    np.save(f"embeddings/{folder_name}/descriptions_embeddings.npy", descriptions_embeddings)
    np.save(f"embeddings/{folder_name}/info_embeddings.npy", info_embeddings)
    print("Saved")

if __name__ == "__main__":
    main()